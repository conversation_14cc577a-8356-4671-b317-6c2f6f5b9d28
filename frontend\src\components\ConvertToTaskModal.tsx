import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowRight } from 'lucide-react';
import { InboxItem, Project } from '@/services/api';
import { useInboxStore } from '@/store/inboxStore';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';


const convertTaskSchema = z.object({
  title: z.string().min(1, 'Task title is required'),
  content: z.string().optional(),
  tagId: z.string().optional(),
  dueDate: z.string().optional(),
  effortEstimate: z.number().min(1).max(1440).optional(),
  projectId: z.string().optional(),
});

type ConvertTaskFormData = z.infer<typeof convertTaskSchema>;

interface ConvertToTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  inboxItem: InboxItem;
  projects: Project[];
  onSuccess: () => void;
}

const ConvertToTaskModal: React.FC<ConvertToTaskModalProps> = ({
  isOpen,
  onClose,
  inboxItem,
  projects,
  onSuccess,
}) => {
  const { processInboxItem } = useInboxStore();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ConvertTaskFormData>({
    resolver: zodResolver(convertTaskSchema),
    defaultValues: {
      title: inboxItem.content.slice(0, 100), // Use first 100 chars as title
      content: inboxItem.content,
    },
  });

  const onSubmit = async (data: ConvertTaskFormData) => {
    try {
      setIsSubmitting(true);

      // Convert form data to API format
      const taskData = {
        title: data.title,
        content: data.content,
        tagId: data.tagId,
        dueDate: data.dueDate ? new Date(data.dueDate).toISOString() : undefined,
        effortEstimate: data.effortEstimate,
        projectId: data.projectId,
      };

      await processInboxItem(inboxItem.id, 'convert_to_task', taskData);
      onSuccess();
    } catch (error) {
      console.error('Failed to convert inbox item to task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Convert to Task"
      size="lg"
    >
      <div className="space-y-6">
        {/* Original Content */}
        <div className="p-4 bg-secondary-50 rounded-lg">
          <h4 className="text-sm font-medium text-secondary-900 mb-2">
            Original Inbox Item:
          </h4>
          <p className="text-sm text-secondary-700">
            {inboxItem.content}
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Title */}
          <Input
            label="Task Title *"
            {...register('title')}
            error={errors.title?.message}
            placeholder="What needs to be done?"
          />

          {/* Content */}
          <div className="space-y-1">
            <label className="block text-sm font-medium text-secondary-700">
              Description
            </label>
            <textarea
              {...register('content')}
              rows={3}
              className="input resize-none"
              placeholder="Additional details about this task..."
            />
            {errors.content && (
              <p className="text-sm text-error-600">{errors.content.message}</p>
            )}
          </div>

          {/* Project */}
          <div className="space-y-1">
            <label className="block text-sm font-medium text-secondary-700">
              Project
            </label>
            <select
              {...register('projectId')}
              className="input"
            >
              <option value="">No project</option>
              {projects.map((project) => (
                <option key={project.id} value={project.id}>
                  {project.name}
                </option>
              ))}
            </select>
          </div>

          {/* Tag Selection */}
          <div className="space-y-1">
            <label className="block text-sm font-medium text-secondary-700">
              Tag
            </label>
            <select
              {...register('tagId')}
              className="input"
            >
              <option value="">No tag</option>
              {/* TODO: Load tags dynamically based on selected project */}
            </select>
          </div>

          {/* Due Date and Effort Estimate */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Due Date"
              type="datetime-local"
              {...register('dueDate')}
              error={errors.dueDate?.message}
            />

            <Input
              label="Effort Estimate (minutes)"
              type="number"
              min="1"
              max="1440"
              {...register('effortEstimate', { valueAsNumber: true })}
              error={errors.effortEstimate?.message}
              placeholder="e.g., 30"
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={isSubmitting}
            >
              <ArrowRight className="h-4 w-4 mr-2" />
              Convert to Task
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default ConvertToTaskModal;
