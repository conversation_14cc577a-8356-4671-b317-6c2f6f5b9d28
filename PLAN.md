# SimpleLife Implementation Plan
## Complete Overhaul for ADHD-Friendly Time Management

### 🎯 **Overview**
This plan addresses all issues and implements requested features to create a seamless, 100% drag-and-drop friendly ADHD time management system with user-defined tags, enhanced timeline functionality, and improved UI/UX.

---

## 📋 **Phase 1: Database Schema & Backend Updates**

### 1.1 Remove Priority System & Implement User-Defined Tags

**REMOVE:**
- `Priority` enum from `schema.prisma`
- `priority` field from `Task` model
- All priority-related validation in backend controllers
- Priority-related API endpoints and filters

**ADD:**
- New `Tag` model with user-defined properties
- Tag-Project relationship (many-to-many)
- Tag-Task relationship (many-to-one)
- Icon selection for tags

**Database Changes:**
```prisma
model Tag {
  id        String   @id @default(cuid())
  name      String
  icon      String   // Lucide icon name
  color     String   // Hex color code
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  projectId String
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  tasks     Task[]

  @@unique([name, projectId]) // Unique tag names per project
  @@index([userId, projectId])
}

model Task {
  // Remove: priority Priority?
  // Add:
  tagId     String?
  tag       Tag?     @relation(fields: [tagId], references: [id], onDelete: SetNull)
  
  // Add new status for pause functionality
  status    TaskStatus @default(TODO) // Update enum to include PAUSED
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  PAUSED      // NEW
  DONE
  ARCHIVED
}
```

### 1.2 Backend API Updates

**NEW Endpoints:**
- `POST /api/projects/:id/tags` - Create tag for project
- `GET /api/projects/:id/tags` - Get all tags for project
- `PUT /api/tags/:id` - Update tag
- `DELETE /api/tags/:id` - Delete tag

**MODIFY Endpoints:**
- Update task creation/update to use `tagId` instead of `priority`
- Add tag filtering to task queries
- Update project endpoints to include tag counts

---

## 📋 **Phase 2: Frontend State Management Updates**

### 2.1 Create Tag Store
**NEW FILE:** `frontend/src/store/tagStore.ts`
- Tag CRUD operations
- Project-tag relationships
- Icon and color management

### 2.2 Update Task Store
**MODIFY:** `frontend/src/store/taskStore.ts`
- Remove priority-related state and actions
- Add tag-related filtering
- Add pause zone functionality
- Add automatic status updates based on drag zones

### 2.3 Update Project Store
**MODIFY:** `frontend/src/store/projectStore.ts`
- Include tag counts in project data
- Add tag management functions

---

## 📋 **Phase 3: Timeline Page - Primary Focus**

### 3.1 Enhanced Timeline Functionality

**CRITICAL IMPROVEMENTS:**
1. **Automatic Task Management:**
   - Tasks past current time → Auto-move to PAUSE zone
   - Tasks dragged to timeline → Status: IN_PROGRESS
   - Tasks dragged to pause → Status: PAUSED
   - Tasks dragged to unscheduled → Status: TODO
   - Tasks dragged to completed → Status: DONE

2. **Visual Improvements:**
   - Increase vertical space for multiple tasks per slot
   - Better task stacking visualization
   - Improved drag feedback and animations

3. **Zone Implementation:**
   - **Not Scheduled Zone:** Unstarted tasks (TODO status)
   - **Pause Zone:** In-progress but temporarily paused tasks (PAUSED status)
   - **Completed Zone:** Finished tasks (DONE status)
   - **Active Timeline:** Currently scheduled tasks (IN_PROGRESS status)

### 3.2 Task Extension & Resizing
**IMPLEMENT:**
- Drag task edges to extend/reduce time slots
- Visual indicators for resize handles
- Real-time duration updates

### 3.3 Current Task Footer
**NEW COMPONENT:** `CurrentTaskFooter.tsx`
- Shows when task is IN_PROGRESS during current time slot
- Displays: Task name, Project, Time slot, Progress actions
- Appears on all pages as floating footer

---

## 📋 **Phase 4: Dashboard Page Restructure**

### 4.1 Remove Daily Schedule
**REMOVE:**
- Time slot grid from Dashboard
- Move to Timeline page with header tabs

### 4.2 New Dashboard Layout
**IMPLEMENT:**
1. **All Today's Tasks Section:**
   - Show all scheduled tasks for current day
   - Regardless of project
   - Visual timeline overview

2. **Priority/Tag Section:**
   - Show all tasks (scheduled + unscheduled) grouped by tags
   - Allow quick access to high-priority items
   - Drag-and-drop to schedule

3. **Header Tabs:**
   - Dashboard View (new layout)
   - Timeline View (moved from separate page)

---

## 📋 **Phase 5: Projects Page Enhancement**

### 5.1 Project List Updates
**MODIFY:** `ProjectsPage.tsx`
- Add tag count display: "X Tags / Y Tasks"
- Only show tag count if project has tags
- Enhanced project creation with tag management

### 5.2 Tag Management Interface
**NEW COMPONENTS:**
- `TagCreator.tsx` - Create tags with icon/color selection
- `TagEditor.tsx` - Edit existing tags
- `IconPicker.tsx` - Lucide icon selection component

### 5.3 Individual Project Page Redesign
**COMPLETE OVERHAUL:**
- Replace list view with Kanban-style layout
- Group tasks by tags (columns)
- Better color scheme and visual design
- Add task creation directly from project page
- Auto-assign project and selected tag to new tasks

---

## 📋 **Phase 6: Inbox Page Improvements**

### 6.1 Drag-and-Drop Fixes
**CRITICAL FIXES:**
- Improve drop zone accuracy and feedback
- Cancel drag when released outside valid zones
- Better visual indicators for valid drop targets
- Smooth animations and transitions

### 6.2 Tag Integration
**REPLACE:**
- Priority levels → User-defined tags
- Update drop zones to show project tags instead of priorities
- Dynamic tag loading per project

---

## 📋 **Phase 7: UI/UX Components**

### 7.1 New Components
**CREATE:**
- `TagBadge.tsx` - Consistent tag display
- `TaskZones.tsx` - Drag zones for different task states
- `CurrentTaskFooter.tsx` - Active task indicator
- `IconPicker.tsx` - Icon selection interface
- `KanbanBoard.tsx` - Project task board layout

### 7.2 Enhanced Drag-and-Drop
**IMPROVE:**
- Better visual feedback during drag
- Smooth animations that don't interfere with functionality
- Touch support for mobile devices
- Accessibility improvements

---

## 📋 **Phase 8: Performance & Polish**

### 8.1 Performance Optimizations
- Virtual scrolling for large task lists
- Memoized components to prevent re-renders
- Optimized drag calculations
- Efficient state updates

### 8.2 Visual Polish
- Consistent color scheme across all components
- Smooth transitions and animations
- Professional, calming design
- ADHD-friendly visual hierarchy

---

## 🗂️ **Files to Modify/Create**

### Database & Backend
- **MODIFY:** `backend/prisma/schema.prisma`
- **CREATE:** `backend/src/controllers/tagController.ts`
- **MODIFY:** `backend/src/controllers/taskController.ts`
- **MODIFY:** `backend/src/controllers/projectController.ts`
- **CREATE:** `backend/src/routes/tagRoutes.ts`
- **MODIFY:** `backend/src/config/validation.ts`

### Frontend Stores
- **CREATE:** `frontend/src/store/tagStore.ts`
- **MODIFY:** `frontend/src/store/taskStore.ts`
- **MODIFY:** `frontend/src/store/projectStore.ts`

### Frontend Pages
- **MODIFY:** `frontend/src/pages/TimelinePage.tsx`
- **MODIFY:** `frontend/src/pages/DashboardPage.tsx`
- **MODIFY:** `frontend/src/pages/ProjectsPage.tsx`
- **MODIFY:** `frontend/src/pages/InboxPage.tsx`
- **CREATE:** `frontend/src/pages/ProjectDetailPage.tsx`

### Frontend Components
- **CREATE:** `frontend/src/components/Tags/TagCreator.tsx`
- **CREATE:** `frontend/src/components/Tags/TagEditor.tsx`
- **CREATE:** `frontend/src/components/Tags/TagBadge.tsx`
- **CREATE:** `frontend/src/components/UI/IconPicker.tsx`
- **CREATE:** `frontend/src/components/Timeline/TaskZones.tsx`
- **CREATE:** `frontend/src/components/Timeline/CurrentTaskFooter.tsx`
- **CREATE:** `frontend/src/components/Projects/KanbanBoard.tsx`
- **MODIFY:** `frontend/src/components/Timeline/HorizontalTimeline.tsx`

### API Services
- **CREATE:** `frontend/src/services/tagService.ts`
- **MODIFY:** `frontend/src/services/api.ts`

---

## ⚡ **Implementation Priority**

1. **CRITICAL:** Database schema changes and migration
2. **CRITICAL:** Tag system implementation (backend + frontend)
3. **CRITICAL:** Timeline functionality and zones
4. **HIGH:** Dashboard restructure and current task footer
5. **HIGH:** Projects page tag management
6. **MEDIUM:** Inbox drag-and-drop improvements
7. **MEDIUM:** Project detail page redesign
8. **LOW:** Performance optimizations and polish

---

## 🎯 **Success Criteria**

- ✅ Complete removal of priority system
- ✅ User-defined tags with icons and colors
- ✅ Timeline as primary focus with all requested features
- ✅ Automatic task status management based on zones
- ✅ Current task footer on all pages
- ✅ Enhanced drag-and-drop accuracy
- ✅ Improved project and dashboard layouts
- ✅ 100% functional drag-and-drop interface
- ✅ ADHD-friendly design and performance

This plan ensures every requirement is addressed systematically while maintaining code quality and user experience.
