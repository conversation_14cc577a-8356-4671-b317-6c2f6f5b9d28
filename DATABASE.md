# SimpleLife Database Schema Documentation

## Overview

SimpleLife uses PostgreSQL with Prisma ORM for type-safe database operations. The schema is designed specifically for ADHD-friendly task management with user-defined tags, visual time management, and seamless drag-and-drop functionality.

## Database Models

### User Model
```prisma
model User {
  id           String   @id @default(cuid())
  email        String   @unique
  name         String?
  passwordHash String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  projects   Project[]
  tasks      Task[]
  inboxItems InboxItem[]
  tags       Tag[]
}
```

**Purpose**: Core user authentication and account management
**Key Features**:
- Unique email-based authentication
- Secure password hashing with bcrypt
- Cascading relationships to all user data

### Project Model
```prisma
model Project {
  id        String   @id @default(cuid())
  name      String
  color     String?  // Optional color for visual distinction
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  tasks Task[]
  tags  Tag[]

  @@index([userId])
}
```

**Purpose**: Organizational contexts for grouping related tasks
**Key Features**:
- Custom colors for visual identification
- One-to-many relationship with tasks and tags
- Cascading deletion when user is deleted

### Tag Model
```prisma
model Tag {
  id        String   @id @default(cuid())
  name      String
  icon      String   // Lucide icon name
  color     String   // Hex color code
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  projectId String
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  tasks     Task[]

  @@unique([name, projectId]) // Unique tag names per project
  @@index([userId, projectId])
}
```

**Purpose**: User-defined categorization system replacing rigid priorities
**Key Features**:
- Custom icons from Lucide icon library
- Custom colors for visual distinction
- Unique tag names per project (prevents duplicates)
- Belongs to specific project and user

### Task Model
```prisma
model Task {
  id             String      @id @default(cuid())
  title          String
  content        String?
  status         TaskStatus  @default(TODO)
  dueDate        DateTime?
  scheduledTime  DateTime?   // When the task is scheduled to be worked on
  effortEstimate Int?        // e.g., in minutes, or story points

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  projectId String? // Can be null if it's a standalone task
  project   Project? @relation(fields: [projectId], references: [id], onDelete: SetNull)

  tagId     String?
  tag       Tag?     @relation(fields: [tagId], references: [id], onDelete: SetNull)

  @@index([userId, status])
  @@index([userId, dueDate])
  @@index([projectId])
  @@index([tagId])
}
```

**Purpose**: Core task entities with simplified structure for ADHD-friendly focus
**Key Features**:
- Optional project assignment (can exist independently)
- Optional tag assignment for flexible categorization
- Scheduled time for timeline placement
- Multiple indexes for efficient querying
- SetNull on project/tag deletion (preserves task)

### InboxItem Model
```prisma
model InboxItem {
  id          String        @id @default(cuid())
  content     String        // The raw captured thought
  status      InboxStatus   @default(UNPROCESSED)
  createdAt   DateTime      @default(now())
  processedAt DateTime?     // When the item was actioned

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, status])
}
```

**Purpose**: Temporary storage for quick thought capture
**Key Features**:
- Raw content storage without forced categorization
- Processing status tracking
- Timestamp for processing workflow
- Efficient querying by user and status

## Enums

### TaskStatus
```prisma
enum TaskStatus {
  TODO        // Not started, in Not Scheduled zone
  IN_PROGRESS // Currently being worked on, in timeline
  PAUSED      // Temporarily stopped, in Pause zone
  DONE        // Completed, in Completed zone
  ARCHIVED    // Long-term storage, in Archived zone
}
```

**Purpose**: Track task lifecycle through drag-and-drop zones
**ADHD Benefits**: Clear visual states, no ambiguous statuses

### InboxStatus
```prisma
enum InboxStatus {
  UNPROCESSED // Newly captured, awaiting processing
  PROCESSED   // Converted to task or handled
  DEFERRED    // Postponed for later processing
}
```

**Purpose**: Track inbox item processing workflow
**ADHD Benefits**: Simple three-state system, clear next actions

## Relationships

### User → Projects (One-to-Many)
- Users can have multiple projects
- Projects belong to one user
- Cascade delete: removing user removes all projects

### Project → Tags (One-to-Many)
- Projects can have multiple tags
- Tags belong to one project
- Unique constraint: tag names must be unique within project

### Project → Tasks (One-to-Many)
- Projects can have multiple tasks
- Tasks can optionally belong to a project
- SetNull: removing project preserves tasks

### Tag → Tasks (One-to-Many)
- Tags can be assigned to multiple tasks
- Tasks can optionally have one tag
- SetNull: removing tag preserves tasks

### User → Tasks (One-to-Many)
- Users can have multiple tasks
- Tasks belong to one user
- Cascade delete: removing user removes all tasks

### User → InboxItems (One-to-Many)
- Users can have multiple inbox items
- Inbox items belong to one user
- Cascade delete: removing user removes all inbox items

## Indexes

### Performance Optimizations
- `User.email`: Unique index for authentication
- `Task.userId + status`: Efficient task filtering by status
- `Task.userId + dueDate`: Quick due date queries
- `Task.projectId`: Fast project-based task retrieval
- `Task.tagId`: Efficient tag-based filtering
- `Tag.userId + projectId`: Quick tag lookup per project
- `InboxItem.userId + status`: Efficient inbox processing

## Migration Strategy

### Current State
The database schema is fully implemented and includes:
- Complete tag system replacing priorities
- PAUSED status for enhanced workflow
- Proper relationships and constraints
- Performance indexes

### Future Considerations
- Potential addition of task duration tracking
- Possible time-block scheduling enhancements
- Consider adding task templates for common workflows

## ADHD-Specific Design Decisions

### Simplified Structure
- No subtasks to reduce complexity
- Clear, unambiguous status states
- Optional relationships to reduce forced decisions

### Flexible Organization
- User-defined tags instead of rigid priorities
- Optional project assignment
- Visual indicators (colors, icons) stored in database

### Performance Focus
- Efficient indexes for common queries
- Minimal required fields
- Fast drag-and-drop operations support

This schema supports the core ADHD-friendly principles of visual organization, reduced complexity, and flexible categorization while maintaining data integrity and performance.
