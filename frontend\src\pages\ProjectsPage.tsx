import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FolderOpen, Plus } from 'lucide-react';
import { useProjectStore } from '@/store/projectStore';
import { useTaskStore } from '@/store/taskStore';
import { useTagStore } from '@/store/tagStore';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import TaskItem from '@/components/TaskItem';
import QuickAdd from '@/components/QuickAdd';
import CreateProjectModal from '@/components/CreateProjectModal';
import TagBadge from '@/components/Tags/TagBadge';



const ProjectsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { projects, currentProject, fetchProjects, fetchProject, isLoading } = useProjectStore();
  const { tasks, fetchTasks } = useTaskStore();
  const { tags, fetchTags } = useTagStore();
  const [showCreateProjectModal, setShowCreateProjectModal] = useState(false);

  useEffect(() => {
    if (id) {
      fetchProject(id);
      fetchTasks({ projectId: id });
      fetchTags();
    } else {
      fetchProjects();
      fetchTags();
    }
  }, [id, fetchProject, fetchProjects, fetchTasks, fetchTags]);

  const handleProjectClick = (projectId: string) => {
    navigate(`/projects/${projectId}`);
  };

  const handleCreateProject = () => {
    setShowCreateProjectModal(true);
  };

  const handleProjectCreated = () => {
    fetchProjects();
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-secondary-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-secondary-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Show specific project
  if (id && currentProject) {
    return (
      <div className="p-6 space-y-6">
        {/* Project Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div
              className="p-2 rounded-lg"
              style={{ backgroundColor: currentProject.color || '#64748b' }}
            >
              <FolderOpen className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-secondary-900">
                {currentProject.name}
              </h1>
              <div className="space-y-1">
                <p className="text-secondary-600">
                  {tasks.length} task{tasks.length !== 1 ? 's' : ''} • {tags.filter(tag => tag.projectId === currentProject.id).length} tag{tags.filter(tag => tag.projectId === currentProject.id).length !== 1 ? 's' : ''}
                </p>
                {/* Project Tags */}
                <div className="flex flex-wrap gap-2">
                  {tags.filter(tag => tag.projectId === currentProject.id).map(tag => (
                    <TagBadge key={tag.id} tag={tag} />
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div></div>
        </div>

        {/* Kanban Board */}
        {tasks.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <FolderOpen className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-secondary-900 mb-2">
                No tasks yet
              </h3>
              <p className="text-secondary-600 mb-4">
                Create your first task to get started with this project.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Todo Column */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                  <h3 className="font-semibold text-secondary-900">To Do</h3>
                  <span className="text-sm text-secondary-500">
                    ({tasks.filter(task => task.status === 'TODO').length})
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {tasks.filter(task => task.status === 'TODO').map(task => (
                  <TaskItem key={task.id} task={task} />
                ))}
                {tasks.filter(task => task.status === 'TODO').length === 0 && (
                  <div className="text-center py-8 text-secondary-500 text-sm">
                    No tasks to do
                  </div>
                )}
              </CardContent>
            </Card>

            {/* In Progress Column */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <h3 className="font-semibold text-secondary-900">In Progress</h3>
                  <span className="text-sm text-secondary-500">
                    ({tasks.filter(task => task.status === 'IN_PROGRESS').length})
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {tasks.filter(task => task.status === 'IN_PROGRESS').map(task => (
                  <TaskItem key={task.id} task={task} />
                ))}
                {tasks.filter(task => task.status === 'IN_PROGRESS').length === 0 && (
                  <div className="text-center py-8 text-secondary-500 text-sm">
                    No tasks in progress
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Paused Column */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                  <h3 className="font-semibold text-secondary-900">Paused</h3>
                  <span className="text-sm text-secondary-500">
                    ({tasks.filter(task => task.status === 'PAUSED').length})
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {tasks.filter(task => task.status === 'PAUSED').map(task => (
                  <TaskItem key={task.id} task={task} />
                ))}
                {tasks.filter(task => task.status === 'PAUSED').length === 0 && (
                  <div className="text-center py-8 text-secondary-500 text-sm">
                    No paused tasks
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Done Column */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <h3 className="font-semibold text-secondary-900">Done</h3>
                  <span className="text-sm text-secondary-500">
                    ({tasks.filter(task => task.status === 'DONE').length})
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {tasks.filter(task => task.status === 'DONE').map(task => (
                  <TaskItem key={task.id} task={task} />
                ))}
                {tasks.filter(task => task.status === 'DONE').length === 0 && (
                  <div className="text-center py-8 text-secondary-500 text-sm">
                    No completed tasks
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        <QuickAdd />
      </div>
    );
  }

  // Show all projects
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary-100 rounded-lg">
            <FolderOpen className="h-6 w-6 text-primary-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-secondary-900">
              Projects
            </h1>
            <p className="text-secondary-600">
              Organize your tasks into meaningful contexts
            </p>
          </div>
        </div>
        <Button onClick={handleCreateProject}>
          <Plus className="h-4 w-4 mr-2" />
          New Project
        </Button>
      </div>

      {/* Projects Grid */}
      {projects.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <FolderOpen className="h-16 w-16 text-secondary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-secondary-900 mb-2">
              No projects yet
            </h3>
            <p className="text-secondary-600 mb-6 max-w-md mx-auto">
              Create your first project to start organizing your tasks into meaningful contexts.
            </p>
            <Button onClick={handleCreateProject}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Project
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => (
            <Card
              key={project.id}
              className="hover:shadow-soft-lg transition-shadow cursor-pointer"
            >
              <CardContent className="p-6">
                <div
                  className="w-full h-full"
                  onClick={() => handleProjectClick(project.id)}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-4 h-4 rounded-full flex-shrink-0"
                        style={{ backgroundColor: project.color || '#64748b' }}
                      />
                      <h3 className="font-semibold text-secondary-900 truncate">
                        {project.name}
                      </h3>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm text-secondary-600">
                      <span>
                        {project._count?.tasks || 0} task{project._count?.tasks !== 1 ? 's' : ''}
                      </span>
                      <span>
                        {tags.filter(tag => tag.projectId === project.id).length} tag{tags.filter(tag => tag.projectId === project.id).length !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <div className="text-xs text-secondary-500">
                      Created {new Date(project.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={showCreateProjectModal}
        onClose={() => setShowCreateProjectModal(false)}
        onSuccess={handleProjectCreated}
      />



      <QuickAdd />
    </div>
  );
};

export default ProjectsPage;
