// Clean Task Zones Component
import React from 'react';
import { useDroppable, useDraggable } from '@dnd-kit/core';
import {
  Clock,
  Pause,
  CheckCircle2,
  Archive,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Task } from '@/services/api';

interface TaskZonesProps {
  unscheduledTasks: Task[];
  pausedTasks: Task[];
  completedTasks: Task[];
  archivedTasks: Task[];
}

const TaskZones: React.FC<TaskZonesProps> = ({
  unscheduledTasks,
  pausedTasks,
  completedTasks,
  archivedTasks
}) => {
  return (
    <div className="h-64 border-t border-gray-200 bg-gray-50 p-4 overflow-y-auto">
      <div className="grid grid-cols-4 gap-4 h-full">
        <TaskZone
          id="unscheduled-section"
          title="Unscheduled"
          icon={<Clock className="h-4 w-4" />}
          tasks={unscheduledTasks}
          color="blue"
          description="Tasks waiting to be scheduled"
          defaultExpanded={true}
        />

        <TaskZone
          id="paused-section"
          title="Paused"
          icon={<Pause className="h-4 w-4" />}
          tasks={pausedTasks}
          color="yellow"
          description="Tasks temporarily paused"
          defaultExpanded={false}
        />

        <TaskZone
          id="completed-section"
          title="Completed"
          icon={<CheckCircle2 className="h-4 w-4" />}
          tasks={completedTasks}
          color="green"
          description="Finished tasks"
          defaultExpanded={false}
        />

        <TaskZone
          id="archived-section"
          title="Archived"
          icon={<Archive className="h-4 w-4" />}
          tasks={archivedTasks}
          color="gray"
          description="Archived tasks"
          defaultExpanded={false}
        />
      </div>
    </div>
  );
};

// Individual Task Zone Component
interface TaskZoneProps {
  id: string;
  title: string;
  icon: React.ReactNode;
  tasks: Task[];
  color: string;
  description: string;
  defaultExpanded?: boolean;
}

const TaskZone: React.FC<TaskZoneProps> = ({
  id,
  title,
  icon,
  tasks,
  color,
  description,
  defaultExpanded = false
}) => {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);

  const { setNodeRef, isOver } = useDroppable({
    id,
  });

  const getColorClasses = () => {
    const colors = {
      blue: {
        bg: 'bg-blue-50',
        border: 'border-blue-200',
        text: 'text-blue-700',
        icon: 'text-blue-600',
        hover: 'hover:bg-blue-100',
        drop: 'bg-blue-100 border-blue-300'
      },
      yellow: {
        bg: 'bg-yellow-50',
        border: 'border-yellow-200',
        text: 'text-yellow-700',
        icon: 'text-yellow-600',
        hover: 'hover:bg-yellow-100',
        drop: 'bg-yellow-100 border-yellow-300'
      },
      green: {
        bg: 'bg-green-50',
        border: 'border-green-200',
        text: 'text-green-700',
        icon: 'text-green-600',
        hover: 'hover:bg-green-100',
        drop: 'bg-green-100 border-green-300'
      },
      gray: {
        bg: 'bg-gray-50',
        border: 'border-gray-200',
        text: 'text-gray-700',
        icon: 'text-gray-600',
        hover: 'hover:bg-gray-100',
        drop: 'bg-gray-100 border-gray-300'
      }
    };
    return colors[color as keyof typeof colors] || colors.gray;
  };

  const colorClasses = getColorClasses();

  return (
    <div className="flex flex-col h-full">
      {/* Zone Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`
          flex items-center justify-between p-3 rounded-lg border-2 transition-all
          ${isOver ? colorClasses.drop : `${colorClasses.bg} ${colorClasses.border} ${colorClasses.hover}`}
        `}
      >
        <div className="flex items-center gap-2">
          <div className={`p-1 rounded ${colorClasses.bg}`}>
            <div className={colorClasses.icon}>
              {icon}
            </div>
          </div>
          <div className="text-left">
            <h3 className={`font-semibold text-sm ${colorClasses.text}`}>
              {title}
            </h3>
            <p className="text-xs text-gray-600">{description}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${colorClasses.bg} ${colorClasses.text}`}>
            {tasks.length}
          </span>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 text-gray-400" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-400" />
          )}
        </div>
      </button>

      {/* Zone Content */}
      {isExpanded && (
        <div
          ref={setNodeRef}
          className={`
            flex-1 mt-2 p-3 rounded-lg border-2 transition-all overflow-y-auto
            ${isOver ? colorClasses.drop : `${colorClasses.bg} ${colorClasses.border}`}
          `}
        >
          {/* Drop Indicator */}
          {isOver && tasks.length === 0 && (
            <div className={`p-4 border-2 border-dashed rounded-lg ${colorClasses.border}`}>
              <p className={`text-center text-sm font-medium ${colorClasses.text}`}>
                Drop task here
              </p>
            </div>
          )}

          {/* Tasks */}
          <div className="space-y-2">
            {tasks.map(task => (
              <ZoneTask key={task.id} task={task} />
            ))}
          </div>

          {/* Empty State */}
          {tasks.length === 0 && !isOver && (
            <div className="text-center py-4">
              <p className="text-sm text-gray-500">No tasks</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Zone Task Component
const ZoneTask: React.FC<{ task: Task }> = ({ task }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        p-2 bg-white border border-gray-200 rounded cursor-grab
        hover:shadow-sm transition-all select-none
        ${isDragging ? 'opacity-50 rotate-1 scale-95' : ''}
      `}
      {...attributes}
      {...listeners}
    >
      <div className="flex items-center gap-2">
        {task.project && (
          <div
            className="w-2 h-2 rounded-full"
            style={{ backgroundColor: task.project.color || '#64748b' }}
          />
        )}
        <span className="text-sm font-medium text-gray-900 truncate">
          {task.title}
        </span>
      </div>
      {task.tag && (
        <div className="mt-1">
          <span
            className="text-xs px-2 py-1 rounded-full"
            style={{
              backgroundColor: `${task.tag.color}20`,
              color: task.tag.color
            }}
          >
            {task.tag.name}
          </span>
        </div>
      )}
    </div>
  );
};

export default TaskZones;
