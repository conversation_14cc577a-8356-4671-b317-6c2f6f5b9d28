// Fixed Timeline Grid Component with Proper Layout
import React from 'react';
import { Task } from '@/services/api';
import { TimeSlot, GridConfig, YAxisSection } from './types';
import TimelineSection from './TimelineSection';

interface TimelineGridProps {
  timeSlots: TimeSlot[];
  tasks: Task[];
  gridConfig: GridConfig;
  sections: YAxisSection[];
  draggedTask: Task | null;
}

const TimelineGrid: React.FC<TimelineGridProps> = ({
  timeSlots,
  gridConfig,
  sections
}) => {
  // Calculate proper dimensions
  const sectionHeight = Math.floor(400 / gridConfig.sectionCount);
  const slotWidth = 120; // Fixed width for proper alignment
  const sectionLabelWidth = 200; // Fixed width for section labels

  // Get current time for styling
  const now = new Date();
  const currentHour = now.getHours();

  return (
    <div className="flex-1 flex flex-col border border-gray-200 relative bg-white">
      {/* Time Header - Fixed Layout */}
      <div className="flex border-b bg-gray-50 sticky top-0 z-20 h-16">
        {/* Section Header - Fixed Width */}
        <div
          className="bg-white border-r border-gray-200 flex items-center justify-center"
          style={{ width: `${sectionLabelWidth}px` }}
        >
          <span className="font-semibold text-gray-700">Sections</span>
        </div>

        {/* Time Slot Headers - Fixed Width Grid */}
        <div className="flex flex-1 overflow-x-auto">
          {timeSlots.map(slot => {
            const isPastTime = slot.hour < currentHour;
            const isCurrentTime = slot.hour === currentHour;

            return (
              <div
                key={slot.id}
                className={`
                  border-r border-gray-200 flex items-center justify-center
                  ${isPastTime ? 'bg-gray-100 text-gray-500' : 'bg-white text-gray-700'}
                  ${isCurrentTime ? 'bg-blue-100 text-blue-700 font-semibold' : ''}
                `}
                style={{ width: `${slotWidth}px`, minWidth: `${slotWidth}px` }}
              >
                <span className="text-sm font-medium">
                  {slot.label}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Grid Sections - Fixed Layout */}
      <div className="flex-1 overflow-auto">
        {sections.slice(0, gridConfig.sectionCount).map((section, index) => (
          <TimelineSection
            key={section.id}
            section={section}
            timeSlots={timeSlots}
            height={sectionHeight}
            sectionLabelWidth={sectionLabelWidth}
            slotWidth={slotWidth}
            isLast={index === gridConfig.sectionCount - 1}
          />
        ))}
      </div>

      {/* Grid Lines Overlay */}
      {gridConfig.showGridLines && (
        <GridLinesOverlay
          sectionCount={gridConfig.sectionCount}
          timeSlotCount={timeSlots.length}
          sectionHeight={sectionHeight}
          sectionLabelWidth={sectionLabelWidth}
          slotWidth={slotWidth}
        />
      )}

      {/* Current Time Line */}
      <CurrentTimeLine
        timeSlots={timeSlots}
        sectionLabelWidth={sectionLabelWidth}
        slotWidth={slotWidth}
      />
    </div>
  );
};

// Grid Lines Overlay Component - Fixed Layout
const GridLinesOverlay: React.FC<{
  sectionCount: number;
  timeSlotCount: number;
  sectionHeight: number;
  sectionLabelWidth: number;
  slotWidth: number;
}> = ({ sectionCount, timeSlotCount, sectionHeight, sectionLabelWidth, slotWidth }) => {
  return (
    <div className="absolute inset-0 pointer-events-none z-10">
      {/* Vertical Grid Lines - Fixed Positioning */}
      {Array.from({ length: timeSlotCount }, (_, index) => (
        <div
          key={`vertical-${index}`}
          className="absolute top-0 bottom-0 border-l border-gray-300 opacity-30"
          style={{
            left: `${sectionLabelWidth + (index + 1) * slotWidth}px`
          }}
        />
      ))}

      {/* Horizontal Grid Lines - Fixed Positioning */}
      {Array.from({ length: sectionCount - 1 }, (_, index) => (
        <div
          key={`horizontal-${index}`}
          className="absolute left-0 right-0 border-t border-gray-300 opacity-30"
          style={{
            top: `${64 + (index + 1) * sectionHeight}px`
          }}
        />
      ))}
    </div>
  );
};

// Current Time Line Component - Fixed Layout
const CurrentTimeLine: React.FC<{
  timeSlots: TimeSlot[];
  sectionLabelWidth: number;
  slotWidth: number;
}> = ({ timeSlots, sectionLabelWidth, slotWidth }) => {
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinutes = now.getMinutes();

  // Find the current time slot
  const currentSlotIndex = timeSlots.findIndex(slot => slot.hour === currentHour);

  if (currentSlotIndex === -1) return null;

  // Calculate exact position within the hour
  const minutePercentage = currentMinutes / 60;
  const leftPosition = sectionLabelWidth + (currentSlotIndex * slotWidth) + (minutePercentage * slotWidth);

  return (
    <div
      className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-30 pointer-events-none"
      style={{ left: `${leftPosition}px` }}
    >
      {/* Current Time Indicator */}
      <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full border-2 border-white shadow-sm" />

      {/* Time Label */}
      <div className="absolute -top-8 -left-8 bg-red-500 text-white text-xs px-2 py-1 rounded shadow-sm whitespace-nowrap">
        {now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </div>
    </div>
  );
};

export default TimelineGrid;
