import React from 'react';
import * as LucideIcons from 'lucide-react';

interface TagBadgeProps {
  tag: {
    id: string;
    name: string;
    icon: string;
    color: string;
    _count?: {
      tasks: number;
    };
  };
  size?: 'sm' | 'md' | 'lg';
  showTaskCount?: boolean;
  onClick?: () => void;
  className?: string;
}

const TagBadge: React.FC<TagBadgeProps> = ({
  tag,
  size = 'md',
  showTaskCount = false,
  onClick,
  className = '',
}) => {
  // Get the icon component dynamically
  const IconComponent = (LucideIcons as any)[tag.icon] || LucideIcons.Tag;

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base',
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };

  // Calculate text color based on background color
  const getTextColor = (backgroundColor: string) => {
    // Remove # if present
    const hex = backgroundColor.replace('#', '');

    // Convert to RGB
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // Return white for dark backgrounds, dark for light backgrounds
    return luminance > 0.5 ? '#1f2937' : '#ffffff';
  };

  const textColor = getTextColor(tag.color);
  const isLight = textColor === '#1f2937';

  return (
    <span
      className={`
        inline-flex items-center gap-1.5 rounded-full font-medium transition-all
        ${sizeClasses[size]}
        ${onClick ? 'cursor-pointer hover:scale-105 hover:shadow-sm' : ''}
        ${className}
      `}
      style={{
        backgroundColor: tag.color,
        color: textColor,
        border: isLight ? `1px solid ${tag.color}dd` : 'none',
      }}
      onClick={onClick}
      title={`${tag.name}${showTaskCount && tag._count ? ` (${tag._count.tasks} tasks)` : ''}`}
    >
      <IconComponent className={iconSizes[size]} />
      <span className="truncate">{tag.name}</span>
      {showTaskCount && tag._count && (
        <span
          className={`
            ml-1 px-1.5 py-0.5 rounded-full text-xs font-bold
            ${isLight ? 'bg-black bg-opacity-10' : 'bg-white bg-opacity-20'}
          `}
        >
          {tag._count.tasks}
        </span>
      )}
    </span>
  );
};

export default TagBadge;
