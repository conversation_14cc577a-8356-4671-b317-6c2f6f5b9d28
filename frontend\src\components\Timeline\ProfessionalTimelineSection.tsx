// Professional Timeline Section - Clean Design
import React from 'react';
import { YAxisSection, TimeSlot } from './types';
import ProfessionalTimelineSlot from './ProfessionalTimelineSlot';

interface ProfessionalTimelineSectionProps {
  section: YAxisSection;
  timeSlots: TimeSlot[];
  height: number;
  sectionLabelWidth: number;
  slotWidth: number;
  isLast: boolean;
  zoom: number;
  timeOffset: number;
}

const ProfessionalTimelineSection: React.FC<ProfessionalTimelineSectionProps> = ({
  section,
  timeSlots,
  height,
  sectionLabelWidth,
  slotWidth,
  isLast,
  zoom,
  timeOffset
}) => {
  const now = new Date();
  const currentHour = now.getHours();

  return (
    <div
      className={`flex ${!isLast ? 'border-b border-gray-200' : ''}`}
      style={{ height: `${height}px` }}
    >
      {/* Professional Section Label */}
      <div
        className="bg-gradient-to-r from-gray-50 to-white border-r border-gray-200 flex items-center p-6"
        style={{ width: `${sectionLabelWidth}px` }}
      >
        <div className="flex items-center gap-4 w-full">
          {/* Section Color Indicator */}
          {section.color && (
            <div
              className="w-6 h-6 rounded-full flex-shrink-0 shadow-sm border-2 border-white"
              style={{ backgroundColor: section.color }}
            />
          )}

          {/* Section Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3">
              <span className="font-semibold text-lg text-gray-900 truncate">
                {section.name}
              </span>
              <span className="text-sm text-gray-600 bg-gray-200 px-3 py-1 rounded-full font-medium">
                {section.tasks.length}
              </span>
            </div>

            {/* Section Type and Time Info */}
            <div className="flex items-center gap-3 mt-2">
              <span className="text-sm text-gray-600 capitalize font-medium">
                {section.type}
              </span>

              {/* Time Zone Info for Adaptive Sections */}
              {section.timeZones.length > 0 && (
                <span className="text-xs text-gray-500 bg-blue-50 px-2 py-1 rounded">
                  {section.timeZones.map(tz =>
                    `${tz.startHour}:00-${tz.endHour}:00`
                  ).join(', ')}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Professional Time Slots - With Time-based Panning */}
      <div
        className="flex"
        style={{
          transform: `translateX(${timeOffset}px)`
        }}
      >
        {timeSlots.map(slot => (
          <ProfessionalTimelineSlot
            key={slot.id}
            slot={slot}
            section={section}
            tasks={section.tasks}
            slotWidth={slotWidth}
            height={height}
            isCurrentTime={slot.hour === currentHour}
            zoom={zoom}
          />
        ))}
      </div>
    </div>
  );
};

export default ProfessionalTimelineSection;
