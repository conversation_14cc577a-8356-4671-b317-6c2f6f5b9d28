// Clean Timeline State Management
import { useState, useMemo } from 'react';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { useTagStore } from '@/store/tagStore';
import {
  GridConfig,
  TimeSlot,
  YAxisSection,
  DEFAULT_GRID_CONFIG
} from '../types';

export const useTimelineState = (currentDate: Date) => {
  const { tasks } = useTaskStore();
  const { projects } = useProjectStore();
  const { tags } = useTagStore();

  // Grid configuration state
  const [gridConfig, setGridConfig] = useState<GridConfig>(DEFAULT_GRID_CONFIG);

  // Generate time slots for the current date - FIXED: Better time range and formatting
  const timeSlots = useMemo((): TimeSlot[] => {
    const slots: TimeSlot[] = [];
    const startHour = 5; // 5 AM
    const endHour = 23; // 11 PM

    for (let hour = startHour; hour <= endHour; hour++) {
      const slotDate = new Date(currentDate);
      slotDate.setHours(hour, 0, 0, 0);

      slots.push({
        id: `slot-${hour}`,
        hour,
        date: slotDate,
        label: formatHour(hour)
      });
    }

    return slots;
  }, [currentDate]);

  // Filter tasks for current date
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      if (!task.scheduledTime) return false;
      const taskDate = new Date(task.scheduledTime);
      return taskDate.toDateString() === currentDate.toDateString();
    });
  }, [tasks, currentDate]);

  // FIXED: Generate sections with proper task distribution
  const sections = useMemo((): YAxisSection[] => {
    return generateSmartSections(gridConfig, filteredTasks, projects, tags);
  }, [gridConfig, filteredTasks, projects, tags]);

  // Get unscheduled tasks
  const unscheduledTasks = useMemo(() => {
    return tasks.filter(task =>
      task.status === 'TODO' && !task.scheduledTime
    );
  }, [tasks]);

  // Get paused tasks
  const pausedTasks = useMemo(() => {
    return tasks.filter(task => task.status === 'PAUSED');
  }, [tasks]);

  // Get completed tasks
  const completedTasks = useMemo(() => {
    return tasks.filter(task => task.status === 'DONE');
  }, [tasks]);

  // Get archived tasks
  const archivedTasks = useMemo(() => {
    return tasks.filter(task => task.status === 'ARCHIVED');
  }, [tasks]);

  return {
    tasks,
    timeSlots,
    gridConfig,
    setGridConfig,
    filteredTasks,
    sections,
    projects,
    tags,
    unscheduledTasks,
    pausedTasks,
    completedTasks,
    archivedTasks
  };
};

// FIXED: Generate smart sections with proper task distribution
const generateSmartSections = (
  gridConfig: GridConfig,
  tasks: any[],
  projects: any[],
  tags: any[]
): YAxisSection[] => {
  const sections: YAxisSection[] = [];

  for (let i = 0; i < gridConfig.sectionCount; i++) {
    const sectionType = gridConfig.sectionTypes[i] || 'none';
    let sectionName = `Section ${i + 1}`;
    let sectionColor: string | undefined;
    let sectionTasks: any[] = [];

    // Distribute tasks based on section type
    if (sectionType === 'projects' && projects.length > 0) {
      const project = projects[i % projects.length];
      if (project) {
        sectionName = project.name;
        sectionColor = project.color;
        sectionTasks = tasks.filter(task => task.project?.id === project.id);
      }
    } else if (sectionType === 'tags' && tags.length > 0) {
      const tag = tags[i % tags.length];
      if (tag) {
        sectionName = tag.name;
        sectionTasks = tasks.filter(task => task.tag?.id === tag.id);
      }
    } else {
      // For 'none' type or when no projects/tags available, distribute tasks evenly
      const tasksPerSection = Math.ceil(tasks.length / gridConfig.sectionCount);
      const startIndex = i * tasksPerSection;
      const endIndex = Math.min(startIndex + tasksPerSection, tasks.length);
      sectionTasks = tasks.slice(startIndex, endIndex);
    }

    sections.push({
      id: `section-${i}`,
      name: sectionName,
      type: sectionType,
      tasks: sectionTasks,
      color: sectionColor,
      timeZones: []
    });
  }

  return sections;
};

// Helper function to format hour display
const formatHour = (hour: number): string => {
  if (hour === 0) return '12 AM';
  if (hour === 12) return '12 PM';
  if (hour < 12) return `${hour} AM`;
  return `${hour - 12} PM`;
};



