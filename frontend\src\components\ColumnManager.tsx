import React, { useState } from 'react';
import { 
  Plus, 
  X, 
  GripVertical, 
  Edit3, 
  Trash2,
  Save,
  Settings
} from 'lucide-react';
import { 
  DndContext,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
} from '@dnd-kit/core';
import {
  SortableContext,
  horizontalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

export interface BoardColumn {
  id: string;
  name: string;
  color: string;
  order: number;
  isDefault: boolean; // Can't be deleted
}

interface ColumnManagerProps {
  columns: BoardColumn[];
  onColumnsChange: (columns: BoardColumn[]) => void;
  isOpen: boolean;
  onClose: () => void;
}

// Default columns that can't be deleted
const DEFAULT_COLUMNS: BoardColumn[] = [
  { id: 'TODO', name: 'To Do', color: 'bg-secondary-100', order: 0, isDefault: true },
  { id: 'IN_PROGRESS', name: 'In Progress', color: 'bg-primary-100', order: 1, isDefault: true },
  { id: 'DONE', name: 'Done', color: 'bg-success-100', order: 2, isDefault: true },
  { id: 'ARCHIVED', name: 'Archived', color: 'bg-secondary-50', order: 3, isDefault: true },
];

// Predefined colors for new columns
const COLUMN_COLORS = [
  'bg-purple-100',
  'bg-pink-100',
  'bg-indigo-100',
  'bg-blue-100',
  'bg-cyan-100',
  'bg-teal-100',
  'bg-emerald-100',
  'bg-lime-100',
  'bg-yellow-100',
  'bg-orange-100',
  'bg-red-100',
  'bg-rose-100',
];

// Sortable Column Item
const SortableColumnItem: React.FC<{
  column: BoardColumn;
  onEdit: (column: BoardColumn) => void;
  onDelete: (id: string) => void;
}> = ({ column, onEdit, onDelete }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: column.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`transition-all ${isDragging ? 'opacity-50 scale-105' : ''}`}
    >
      <Card className="border border-secondary-200">
        <CardContent className="p-3">
          <div className="flex items-center space-x-3">
            {/* Drag Handle */}
            <div
              {...attributes}
              {...listeners}
              className="cursor-grab active:cursor-grabbing text-secondary-400 hover:text-secondary-600"
            >
              <GripVertical className="h-4 w-4" />
            </div>

            {/* Column Preview */}
            <div className={`w-4 h-4 rounded ${column.color}`} />

            {/* Column Name */}
            <span className="flex-1 font-medium text-secondary-900">
              {column.name}
            </span>

            {/* Actions */}
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(column)}
                className="p-1"
              >
                <Edit3 className="h-3 w-3" />
              </Button>
              
              {!column.isDefault && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(column.id)}
                  className="p-1 text-error-600 hover:text-error-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const ColumnManager: React.FC<ColumnManagerProps> = ({ 
  columns, 
  onColumnsChange, 
  isOpen, 
  onClose 
}) => {
  const [localColumns, setLocalColumns] = useState<BoardColumn[]>(columns);
  const [editingColumn, setEditingColumn] = useState<BoardColumn | null>(null);
  const [newColumnName, setNewColumnName] = useState('');
  const [selectedColor, setSelectedColor] = useState(COLUMN_COLORS[0]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = localColumns.findIndex(col => col.id === active.id);
      const newIndex = localColumns.findIndex(col => col.id === over?.id);

      const newColumns = [...localColumns];
      const [reorderedColumn] = newColumns.splice(oldIndex, 1);
      newColumns.splice(newIndex, 0, reorderedColumn);

      // Update order values
      const updatedColumns = newColumns.map((col, index) => ({
        ...col,
        order: index,
      }));

      setLocalColumns(updatedColumns);
    }
  };

  const handleAddColumn = () => {
    if (!newColumnName.trim()) return;

    const newColumn: BoardColumn = {
      id: `CUSTOM_${Date.now()}`,
      name: newColumnName.trim(),
      color: selectedColor,
      order: localColumns.length,
      isDefault: false,
    };

    setLocalColumns([...localColumns, newColumn]);
    setNewColumnName('');
    setSelectedColor(COLUMN_COLORS[0]);
  };

  const handleEditColumn = (updatedColumn: BoardColumn) => {
    setLocalColumns(localColumns.map(col => 
      col.id === updatedColumn.id ? updatedColumn : col
    ));
    setEditingColumn(null);
  };

  const handleDeleteColumn = (id: string) => {
    setLocalColumns(localColumns.filter(col => col.id !== id));
  };

  const handleSave = () => {
    onColumnsChange(localColumns);
    onClose();
  };

  const handleReset = () => {
    setLocalColumns(DEFAULT_COLUMNS);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl max-h-[80vh] overflow-hidden">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-primary-600" />
              <h2 className="text-lg font-semibold text-secondary-900">
                Manage Board Columns
              </h2>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6 overflow-y-auto">
          {/* Current Columns */}
          <div>
            <h3 className="font-medium text-secondary-900 mb-3">
              Current Columns (drag to reorder)
            </h3>
            
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={localColumns.map(col => col.id)}
                strategy={horizontalListSortingStrategy}
              >
                <div className="space-y-2">
                  {localColumns
                    .sort((a, b) => a.order - b.order)
                    .map((column) => (
                      <SortableColumnItem
                        key={column.id}
                        column={column}
                        onEdit={setEditingColumn}
                        onDelete={handleDeleteColumn}
                      />
                    ))}
                </div>
              </SortableContext>
            </DndContext>
          </div>

          {/* Add New Column */}
          <div>
            <h3 className="font-medium text-secondary-900 mb-3">
              Add New Column
            </h3>
            
            <div className="space-y-3">
              <Input
                value={newColumnName}
                onChange={(e) => setNewColumnName(e.target.value)}
                placeholder="Column name (e.g., 'Review', 'Backlog', 'Testing')"
                onKeyPress={(e) => e.key === 'Enter' && handleAddColumn()}
              />
              
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Choose Color
                </label>
                <div className="flex flex-wrap gap-2">
                  {COLUMN_COLORS.map((color) => (
                    <button
                      key={color}
                      onClick={() => setSelectedColor(color)}
                      className={`w-8 h-8 rounded ${color} border-2 transition-all ${
                        selectedColor === color 
                          ? 'border-primary-500 scale-110' 
                          : 'border-secondary-300 hover:border-secondary-400'
                      }`}
                    />
                  ))}
                </div>
              </div>
              
              <Button
                onClick={handleAddColumn}
                disabled={!newColumnName.trim()}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Column
              </Button>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-between pt-4 border-t">
            <Button variant="ghost" onClick={handleReset}>
              Reset to Default
            </Button>
            
            <div className="flex space-x-2">
              <Button variant="ghost" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Edit Column Modal */}
      {editingColumn && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <Card className="w-full max-w-md">
            <CardHeader>
              <h3 className="text-lg font-semibold">Edit Column</h3>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                value={editingColumn.name}
                onChange={(e) => setEditingColumn({
                  ...editingColumn,
                  name: e.target.value
                })}
                placeholder="Column name"
              />
              
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Color
                </label>
                <div className="flex flex-wrap gap-2">
                  {COLUMN_COLORS.map((color) => (
                    <button
                      key={color}
                      onClick={() => setEditingColumn({
                        ...editingColumn,
                        color
                      })}
                      className={`w-6 h-6 rounded ${color} border-2 ${
                        editingColumn.color === color 
                          ? 'border-primary-500' 
                          : 'border-secondary-300'
                      }`}
                    />
                  ))}
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button variant="ghost" onClick={() => setEditingColumn(null)}>
                  Cancel
                </Button>
                <Button onClick={() => handleEditColumn(editingColumn)}>
                  Save
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default ColumnManager;
