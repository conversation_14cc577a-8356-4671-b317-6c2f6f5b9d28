import React, { useState, useEffect } from 'react';
import {
  Save,
  X,
  Tag as TagIcon
} from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { useTagStore } from '@/store/tagStore';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import TagBadge from '@/components/Tags/TagBadge';

interface InlineTaskEditorProps {
  task: Task;
  onSave: () => void;
  onCancel: () => void;
}

const InlineTaskEditor: React.FC<InlineTaskEditorProps> = ({ task, onSave, onCancel }) => {
  const { updateTask } = useTaskStore();
  const { projects } = useProjectStore();
  const { fetchProjectTags } = useTagStore();

  const [formData, setFormData] = useState({
    title: task.title,
    content: task.content || '',
    tagId: task.tagId || '',
    effortEstimate: task.effortEstimate || '',
    dueDate: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : '',
    projectId: task.projectId || '',
  });

  const [availableTags, setAvailableTags] = useState<any[]>([]);

  // Fetch tags when project changes
  useEffect(() => {
    if (formData.projectId) {
      fetchProjectTags(formData.projectId).then(setAvailableTags);
    } else {
      setAvailableTags([]);
    }
  }, [formData.projectId, fetchProjectTags]);



  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!formData.title.trim()) return;

    setIsLoading(true);
    try {
      await updateTask(task.id, {
        title: formData.title,
        content: formData.content || undefined,
        tagId: formData.tagId || undefined,
        effortEstimate: formData.effortEstimate ? parseInt(formData.effortEstimate.toString()) : undefined,
        dueDate: formData.dueDate ? new Date(formData.dueDate).toISOString() : undefined,
        projectId: formData.projectId || undefined,
      });
      onSave();
    } catch (error) {
      console.error('Failed to update task:', error);
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <Card className="border-2 border-primary-200 shadow-lg">
      <CardContent className="p-4 space-y-4">
        {/* Title */}
        <div>
          <Input
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            placeholder="Task title..."
            className="text-lg font-medium"
            autoFocus
          />
        </div>

        {/* Content */}
        <div>
          <textarea
            value={formData.content}
            onChange={(e) => setFormData({ ...formData, content: e.target.value })}
            placeholder="Add description..."
            className="w-full p-2 border border-secondary-300 rounded-md resize-none"
            rows={3}
          />
        </div>

        {/* Properties Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Tag Selection */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Tag
            </label>
            {availableTags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => setFormData({ ...formData, tagId: '' })}
                  className={`flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium transition-all ${
                    !formData.tagId
                      ? 'bg-secondary-200 text-secondary-800 ring-2 ring-secondary-400'
                      : 'bg-secondary-100 text-secondary-600 hover:bg-secondary-200'
                  }`}
                >
                  <TagIcon className="h-3 w-3" />
                  <span>No Tag</span>
                </button>
                {availableTags.map((tag) => (
                  <button
                    key={tag.id}
                    onClick={() => setFormData({ ...formData, tagId: tag.id })}
                    className={`transition-all ${
                      formData.tagId === tag.id ? 'ring-2 ring-primary-400' : ''
                    }`}
                  >
                    <TagBadge tag={tag} size="sm" />
                  </button>
                ))}
              </div>
            ) : (
              <p className="text-sm text-secondary-500">
                {formData.projectId ? 'No tags available for this project' : 'Select a project to see available tags'}
              </p>
            )}
          </div>

          {/* Effort Estimate */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Effort (minutes)
            </label>
            <Input
              type="number"
              value={formData.effortEstimate}
              onChange={(e) => setFormData({ ...formData, effortEstimate: e.target.value })}
              placeholder="30"
              min="1"
              max="1440"
            />
          </div>

          {/* Due Date */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Due Date
            </label>
            <Input
              type="date"
              value={formData.dueDate}
              onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
            />
          </div>

          {/* Project */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Project
            </label>
            <select
              value={formData.projectId}
              onChange={(e) => setFormData({ ...formData, projectId: e.target.value })}
              className="w-full p-2 border border-secondary-300 rounded-md"
            >
              <option value="">No Project</option>
              {projects.map((project) => (
                <option key={project.id} value={project.id}>
                  {project.name}
                </option>
              ))}
            </select>
          </div>
        </div>



        {/* Actions */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button
            variant="ghost"
            onClick={onCancel}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-1" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!formData.title.trim() || isLoading}
          >
            <Save className="h-4 w-4 mr-1" />
            {isLoading ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default InlineTaskEditor;
