import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { tagService, Tag } from '@/services/tagService';

interface TagState {
  tags: Tag[];
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchTags: () => Promise<void>;
  fetchProjectTags: (projectId: string) => Promise<Tag[]>;
  createTag: (projectId: string, tagData: Omit<Tag, 'id' | 'createdAt' | 'updatedAt' | 'userId' | 'projectId'>) => Promise<Tag>;
  updateTag: (tagId: string, tagData: Partial<Omit<Tag, 'id' | 'createdAt' | 'updatedAt' | 'userId' | 'projectId'>>) => Promise<Tag>;
  deleteTag: (tagId: string) => Promise<void>;
  clearError: () => void;
}

export const useTagStore = create<TagState>()(
  devtools(
    (set, get) => ({
      tags: [],
      loading: false,
      error: null,

      fetchTags: async () => {
        try {
          set({ loading: true, error: null });
          const tags = await tagService.getAllTags();
          set({ tags, loading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tags';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      fetchProjectTags: async (projectId: string) => {
        try {
          set({ loading: true, error: null });
          const tags = await tagService.getProjectTags(projectId);
          
          // Update the store with project tags (merge with existing tags)
          const currentTags = get().tags;
          const updatedTags = currentTags.filter(tag => tag.projectId !== projectId);
          updatedTags.push(...tags);
          
          set({ tags: updatedTags, loading: false });
          return tags;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch project tags';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      createTag: async (projectId: string, tagData) => {
        try {
          set({ loading: true, error: null });
          const newTag = await tagService.createTag(projectId, tagData);
          
          // Add the new tag to the store
          const currentTags = get().tags;
          set({ tags: [...currentTags, newTag], loading: false });
          
          return newTag;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to create tag';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      updateTag: async (tagId: string, tagData) => {
        try {
          set({ loading: true, error: null });
          const updatedTag = await tagService.updateTag(tagId, tagData);
          
          // Update the tag in the store
          const currentTags = get().tags;
          const updatedTags = currentTags.map(tag => 
            tag.id === tagId ? updatedTag : tag
          );
          set({ tags: updatedTags, loading: false });
          
          return updatedTag;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update tag';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      deleteTag: async (tagId: string) => {
        try {
          set({ loading: true, error: null });
          await tagService.deleteTag(tagId);
          
          // Remove the tag from the store
          const currentTags = get().tags;
          const updatedTags = currentTags.filter(tag => tag.id !== tagId);
          set({ tags: updatedTags, loading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to delete tag';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'tag-store',
    }
  )
);
