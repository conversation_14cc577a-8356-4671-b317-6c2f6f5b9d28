import React, { useState } from 'react';
import {
  Clock,
  AlertTriangle,
  MoreHorizontal,
  Calendar,
  FolderOpen,
  Edit3
} from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { cn } from '@/utils/cn';
import { isOverdue, getDateDescription } from '@/utils/date';
import InlineTaskEditor from '@/components/InlineTaskEditor';
import TagBadge from '@/components/Tags/TagBadge';

interface TaskItemProps {
  task: Task;
  showProject?: boolean;
  className?: string;
  allowEdit?: boolean;
}

const TaskItem: React.FC<TaskItemProps> = ({
  task,
  showProject = false,
  className,
  allowEdit = true
}) => {
  const { fetchTasks } = useTaskStore();
  const [isEditing, setIsEditing] = useState(false);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = async () => {
    setIsEditing(false);
    await fetchTasks(); // Refresh to get updated data
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DONE':
        return 'text-success-600 bg-success-50';
      case 'IN_PROGRESS':
        return 'text-primary-600 bg-primary-50';
      case 'TODO':
        return 'text-secondary-600 bg-secondary-50';
      case 'ARCHIVED':
        return 'text-secondary-500 bg-secondary-50';
      default:
        return 'text-secondary-500 bg-secondary-50';
    }
  };

  const isTaskOverdue = task.dueDate && isOverdue(task.dueDate) && task.status !== 'DONE';

  // If editing, show the inline editor
  if (isEditing) {
    return (
      <div className={className}>
        <InlineTaskEditor
          task={task}
          onSave={handleSaveEdit}
          onCancel={handleCancelEdit}
        />
      </div>
    );
  }

  return (
    <div
      className={cn(
        'group rounded-lg border border-secondary-200 bg-white p-4 transition-all hover:border-secondary-300 hover:shadow-soft',
        task.status === 'DONE' && 'opacity-75',
        isTaskOverdue && 'border-error-200 bg-error-50',
        className
      )}
    >
      {/* Content */}
      <div className="w-full">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            {/* Title */}
            <h3
              className={cn(
                'text-sm font-medium text-secondary-900',
                task.status === 'DONE' && 'line-through text-secondary-600'
              )}
            >
              {task.title}
            </h3>

            {/* Content */}
            {task.content && (
              <p className="mt-1 text-sm text-secondary-600 line-clamp-2">
                {task.content}
              </p>
            )}

            {/* Meta information */}
            <div className="mt-2 flex flex-wrap items-center gap-2 text-xs">
              {/* Project */}
              {showProject && task.project && (
                <div className="flex items-center space-x-1 text-secondary-600">
                  <FolderOpen className="h-3 w-3" />
                  <span>{task.project.name}</span>
                  {task.project.color && (
                    <div
                      className="h-2 w-2 rounded-full"
                      style={{ backgroundColor: task.project.color }}
                    />
                  )}
                </div>
              )}

              {/* Due date */}
              {task.dueDate && (
                <div
                  className={cn(
                    'flex items-center space-x-1',
                    isTaskOverdue ? 'text-error-600' : 'text-secondary-600'
                  )}
                >
                  <Calendar className="h-3 w-3" />
                  <span>{getDateDescription(task.dueDate)}</span>
                  {isTaskOverdue && <AlertTriangle className="h-3 w-3" />}
                </div>
              )}

              {/* Effort estimate */}
              {task.effortEstimate && (
                <div className="flex items-center space-x-1 text-secondary-600">
                  <Clock className="h-3 w-3" />
                  <span>{task.effortEstimate}m</span>
                </div>
              )}

              {/* Tag */}
              {task.tag && (
                <TagBadge tag={task.tag} size="sm" />
              )}

              {/* Status */}
              <span
                className={cn(
                  'inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium',
                  getStatusColor(task.status)
                )}
              >
                {task.status.toLowerCase().replace('_', ' ')}
              </span>


            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {allowEdit && (
              <button
                onClick={handleEdit}
                className="rounded-md p-1 text-secondary-400 hover:bg-secondary-100 hover:text-secondary-600 focus-ring"
                title="Edit task"
              >
                <Edit3 className="h-4 w-4" />
              </button>
            )}

            <button className="rounded-md p-1 text-secondary-400 hover:bg-secondary-100 hover:text-secondary-600 focus-ring">
              <MoreHorizontal className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskItem;
