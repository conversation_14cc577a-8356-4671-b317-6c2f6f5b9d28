// Clean Drag and Drop Logic
import { useState, useCallback } from 'react';
import {
  DragStartEvent,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { DragData } from '../types';

export const useTaskDrop = (tasks: Task[], currentDate?: Date) => {
  const { updateTask } = useTaskStore();
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);

  // Configure sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3, // 3px movement to start drag
      },
    })
  );

  // Handle drag start
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const task = tasks.find(t => t.id === event.active.id);
    setDraggedTask(task || null);
  }, [tasks]);

  // Handle drag end
  const handleDragEnd = useCallback(async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedTask(null);

    if (!over || !active) return;

    const taskId = active.id as string;
    const dropZoneId = over.id as string;
    const dropData = over.data?.current as DragData;

    try {
      // Handle different drop zones
      if (dropZoneId.includes('unscheduled-section')) {
        // Dropped in unscheduled zone
        await updateTask(taskId, {
          status: 'TODO',
          scheduledTime: undefined
        });
      } else if (dropZoneId.includes('paused-section')) {
        // Dropped in paused zone
        await updateTask(taskId, {
          status: 'PAUSED',
          scheduledTime: undefined
        });
      } else if (dropZoneId.includes('completed-section')) {
        // Dropped in completed zone
        await updateTask(taskId, {
          status: 'DONE'
        });
      } else if (dropZoneId.includes('archived-section')) {
        // Dropped in archived zone
        await updateTask(taskId, {
          status: 'ARCHIVED'
        });
      } else if (dropData?.sectionId && (dropData?.slotId || dropData?.hour !== undefined)) {
        // Dropped in timeline grid - check for both slotId and hour
        console.log('🎯 Timeline drop detected:', dropData);
        await handleTimelineSlotDrop(taskId, dropData);
      } else {
        console.log('❌ Unknown drop zone:', { dropZoneId, dropData });
      }
    } catch (error) {
      console.error('Failed to update task:', error);
      // TODO: Show error notification
    }
  }, [updateTask]);

  // FIXED: Handle timeline slot drop with COLLISION DETECTION
  const handleTimelineSlotDrop = async (taskId: string, dropData: DragData) => {
    console.log('🎯 Task Drop Debug:', { taskId, dropData });

    const { slotId, hour } = dropData;

    // Use hour from dropData (most reliable) or extract from slotId
    let targetHour: number;
    if (hour !== undefined) {
      targetHour = hour;
    } else {
      // Extract hour from slot ID format: "slot-{hour}"
      const hourMatch = slotId.match(/slot-(\d+)/);
      if (hourMatch) {
        targetHour = parseInt(hourMatch[1]);
      } else {
        console.error('❌ Could not extract hour from slotId:', slotId);
        return;
      }
    }

    // FIXED: Use the timeline's current date, not today's date
    const targetDate = currentDate || new Date();

    // COLLISION DETECTION: Check if slot is already occupied
    const existingTaskInSlot = tasks.find(task => {
      if (!task.scheduledTime || task.id === taskId) return false;
      const taskDate = new Date(task.scheduledTime);
      return taskDate.getHours() === targetHour &&
             taskDate.toDateString() === targetDate.toDateString();
    });

    if (existingTaskInSlot) {
      console.log('❌ COLLISION DETECTED: Slot already occupied by task:', existingTaskInSlot.title);
      // CANCEL THE MOVEMENT - Don't update the task
      return;
    }

    console.log('✅ Slot is free, scheduling task to hour:', targetHour);

    // Create new scheduled time for the target date
    const scheduledDate = new Date(targetDate);
    scheduledDate.setHours(targetHour, 0, 0, 0);

    console.log('📅 Final scheduled time:', scheduledDate.toISOString());

    // Update task with new schedule and status
    await updateTask(taskId, {
      scheduledTime: scheduledDate.toISOString(),
      status: 'IN_PROGRESS'
    });

    console.log('✅ Task successfully moved to slot');
  };



  return {
    draggedTask,
    handleDragStart,
    handleDragEnd,
    sensors
  };
};
