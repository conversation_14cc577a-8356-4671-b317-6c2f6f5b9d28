import { Router } from 'express';
import { TagController } from '../controllers/tagController';
import { protectRoute } from '../middlewares/authMiddleware';

const router = Router();

// All tag routes require authentication
router.use(protectRoute);

// Project-specific tag routes
router.post('/projects/:projectId/tags', TagController.createTag);
router.get('/projects/:projectId/tags', TagController.getProjectTags);

// Individual tag routes
router.put('/tags/:id', TagController.updateTag);
router.delete('/tags/:id', TagController.deleteTag);

// User tag routes
router.get('/tags', TagController.getUserTags);

export default router;
