import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Validation schemas
const createTagSchema = z.object({
  name: z.string().min(1, 'Tag name is required').max(50, 'Tag name too long'),
  icon: z.string().min(1, 'Icon is required'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
});

const updateTagSchema = z.object({
  name: z.string().min(1, 'Tag name is required').max(50, 'Tag name too long').optional(),
  icon: z.string().min(1, 'Icon is required').optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format').optional(),
});

const idParamSchema = z.object({
  id: z.string().cuid('Invalid tag ID'),
});

const projectIdParamSchema = z.object({
  projectId: z.string().cuid('Invalid project ID'),
});

export class TagController {
  /**
   * Create a new tag for a project
   */
  static async createTag(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      // Validate params and body
      const { projectId } = projectIdParamSchema.parse(req.params);
      const { name, icon, color } = createTagSchema.parse(req.body);

      // Verify project ownership
      const project = await prisma.project.findFirst({
        where: {
          id: projectId,
          userId: req.user.id,
        },
      });

      if (!project) {
        return res.status(404).json({
          status: 'error',
          message: 'Project not found',
        });
      }

      // Check if tag name already exists in this project
      const existingTag = await prisma.tag.findFirst({
        where: {
          name,
          projectId,
        },
      });

      if (existingTag) {
        return res.status(400).json({
          status: 'error',
          message: 'Tag name already exists in this project',
        });
      }

      // Create tag
      const tag = await prisma.tag.create({
        data: {
          name,
          icon,
          color,
          userId: req.user.id,
          projectId,
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              color: true,
            },
          },
          _count: {
            select: {
              tasks: true,
            },
          },
        },
      });

      res.status(201).json({
        status: 'success',
        message: 'Tag created successfully',
        data: {
          tag,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all tags for a project
   */
  static async getProjectTags(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const { projectId } = projectIdParamSchema.parse(req.params);

      // Verify project ownership
      const project = await prisma.project.findFirst({
        where: {
          id: projectId,
          userId: req.user.id,
        },
      });

      if (!project) {
        return res.status(404).json({
          status: 'error',
          message: 'Project not found',
        });
      }

      // Get tags with task counts
      const tags = await prisma.tag.findMany({
        where: {
          projectId,
          userId: req.user.id,
        },
        include: {
          _count: {
            select: {
              tasks: true,
            },
          },
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      res.json({
        status: 'success',
        data: {
          tags,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a tag
   */
  static async updateTag(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const { id } = idParamSchema.parse(req.params);
      const updateData = updateTagSchema.parse(req.body);

      // Find tag and verify ownership
      const existingTag = await prisma.tag.findFirst({
        where: {
          id,
          userId: req.user.id,
        },
      });

      if (!existingTag) {
        return res.status(404).json({
          status: 'error',
          message: 'Tag not found',
        });
      }

      // Check for name conflicts if name is being updated
      if (updateData.name && updateData.name !== existingTag.name) {
        const nameConflict = await prisma.tag.findFirst({
          where: {
            name: updateData.name,
            projectId: existingTag.projectId,
            id: { not: id },
          },
        });

        if (nameConflict) {
          return res.status(400).json({
            status: 'error',
            message: 'Tag name already exists in this project',
          });
        }
      }

      // Update tag
      const updatedTag = await prisma.tag.update({
        where: { id },
        data: updateData,
        include: {
          project: {
            select: {
              id: true,
              name: true,
              color: true,
            },
          },
          _count: {
            select: {
              tasks: true,
            },
          },
        },
      });

      res.json({
        status: 'success',
        message: 'Tag updated successfully',
        data: {
          tag: updatedTag,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a tag
   */
  static async deleteTag(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const { id } = idParamSchema.parse(req.params);

      // Find tag and verify ownership
      const tag = await prisma.tag.findFirst({
        where: {
          id,
          userId: req.user.id,
        },
        include: {
          _count: {
            select: {
              tasks: true,
            },
          },
        },
      });

      if (!tag) {
        return res.status(404).json({
          status: 'error',
          message: 'Tag not found',
        });
      }

      // Delete tag (tasks will have their tagId set to null due to SetNull)
      await prisma.tag.delete({
        where: { id },
      });

      res.json({
        status: 'success',
        message: `Tag deleted successfully. ${tag._count.tasks} tasks were untagged.`,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all tags for a user (across all projects)
   */
  static async getUserTags(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const tags = await prisma.tag.findMany({
        where: {
          userId: req.user.id,
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              color: true,
            },
          },
          _count: {
            select: {
              tasks: true,
            },
          },
        },
        orderBy: [
          { project: { name: 'asc' } },
          { createdAt: 'asc' },
        ],
      });

      res.json({
        status: 'success',
        data: {
          tags,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
