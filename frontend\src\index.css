@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-secondary-200;
  }

  body {
    @apply bg-secondary-50 text-secondary-900;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Custom button styles */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus-visible:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus-visible:ring-secondary-500;
  }

  .btn-ghost {
    @apply hover:bg-secondary-100 hover:text-secondary-900 focus-visible:ring-secondary-500;
  }

  .btn-destructive {
    @apply bg-error-600 text-white hover:bg-error-700 focus-visible:ring-error-500;
  }

  /* Custom input styles */
  .input {
    @apply flex h-10 w-full rounded-lg border border-secondary-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Custom card styles */
  .card {
    @apply rounded-xl border border-secondary-200 bg-white shadow-soft;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  /* Focus styles for USER-friendly navigation */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2;
  }

  /* Calm animations */
  .animate-gentle {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Tag indicators - replaced priority system */
  .tag-indicator {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
  }

  /* Status indicators */
  .status-todo {
    @apply bg-secondary-100 text-secondary-700;
  }

  .status-in-progress {
    @apply bg-primary-100 text-primary-700;
  }

  .status-done {
    @apply bg-success-100 text-success-700;
  }

  .status-archived {
    @apply bg-secondary-50 text-secondary-500;
  }

  /* Drag and Drop styles */
  .draggable {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  .dragging {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    pointer-events: none;
  }

  .drag-handle {
    cursor: grab;
    user-select: none;
    -webkit-user-select: none;
  }

  .drag-handle:active {
    cursor: grabbing;
  }

  /* Prevent text selection during drag operations */
  .dnd-context {
    user-select: none;
    -webkit-user-select: none;
  }

  .dnd-context * {
    user-select: none;
    -webkit-user-select: none;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
