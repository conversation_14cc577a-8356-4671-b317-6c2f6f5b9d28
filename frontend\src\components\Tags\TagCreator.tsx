import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';
import * as LucideIcons from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import IconPicker from '@/components/ui/IconPicker';
import { useTagStore } from '@/store/tagStore';
import { CreateTagData } from '@/services/tagService';

interface TagCreatorProps {
  projectId: string;
  onTagCreated?: (tag: any) => void;
  onCancel?: () => void;
  className?: string;
}

// Predefined color palette for tags
const COLOR_PALETTE = [
  '#EF4444', // Red
  '#F97316', // Orange
  '#F59E0B', // Amber
  '#EAB308', // Yellow
  '#84CC16', // Lime
  '#22C55E', // Green
  '#10B981', // Emerald
  '#14B8A6', // Teal
  '#06B6D4', // <PERSON>an
  '#0EA5E9', // Sky
  '#3B82F6', // Blue
  '#6366F1', // Indigo
  '#8B5CF6', // Violet
  '#A855F7', // Purple
  '#D946EF', // Fuchsia
  '#EC4899', // Pink
  '#F43F5E', // Rose
  '#64748B', // Slate
];

const TagCreator: React.FC<TagCreatorProps> = ({
  projectId,
  onTagCreated,
  onCancel,
  className = '',
}) => {
  const [name, setName] = useState('');
  const [selectedIcon, setSelectedIcon] = useState('Tag');
  const [selectedColor, setSelectedColor] = useState(COLOR_PALETTE[0]);
  const [showIconPicker, setShowIconPicker] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { createTag, error } = useTagStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) return;

    try {
      setIsSubmitting(true);

      const tagData: CreateTagData = {
        name: name.trim(),
        icon: selectedIcon,
        color: selectedColor,
      };

      const newTag = await createTag(projectId, tagData);

      // Reset form
      setName('');
      setSelectedIcon('Tag');
      setSelectedColor(COLOR_PALETTE[0]);

      onTagCreated?.(newTag);
    } catch (error) {
      console.error('Failed to create tag:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setName('');
    setSelectedIcon('Tag');
    setSelectedColor(COLOR_PALETTE[0]);
    onCancel?.();
  };

  // Get the selected icon component
  const SelectedIconComponent = (LucideIcons as any)[selectedIcon] || LucideIcons.Tag;

  return (
    <>
      <div className={`bg-white border border-secondary-200 rounded-lg p-4 ${className}`}>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-semibold text-secondary-900">Create New Tag</h3>
            {onCancel && (
              <Button variant="ghost" size="sm" onClick={handleCancel}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Tag Name */}
          <div>
            <label className="block text-xs font-medium text-secondary-700 mb-1">
              Tag Name
            </label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter tag name..."
              maxLength={50}
              required
            />
          </div>

          {/* Icon Selection */}
          <div>
            <label className="block text-xs font-medium text-secondary-700 mb-2">
              Icon
            </label>
            <button
              type="button"
              onClick={() => setShowIconPicker(true)}
              className="flex items-center gap-2 px-3 py-2 border border-secondary-300 rounded-lg hover:border-secondary-400 transition-colors"
            >
              <SelectedIconComponent className="h-4 w-4 text-secondary-600" />
              <span className="text-sm text-secondary-700">{selectedIcon}</span>
            </button>
          </div>

          {/* Color Selection */}
          <div>
            <label className="block text-xs font-medium text-secondary-700 mb-2">
              Color
            </label>
            <div className="grid grid-cols-9 gap-2">
              {COLOR_PALETTE.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setSelectedColor(color)}
                  className={`w-8 h-8 rounded-full border-2 transition-all hover:scale-110 ${
                    selectedColor === color
                      ? 'border-secondary-400 ring-2 ring-secondary-200'
                      : 'border-secondary-200 hover:border-secondary-300'
                  }`}
                  style={{ backgroundColor: color }}
                  title={color}
                />
              ))}
            </div>
          </div>

          {/* Preview */}
          <div>
            <label className="block text-xs font-medium text-secondary-700 mb-2">
              Preview
            </label>
            <div className="flex items-center">
              <span
                className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium"
                style={{
                  backgroundColor: selectedColor,
                  color: getTextColor(selectedColor),
                }}
              >
                <SelectedIconComponent className="h-4 w-4" />
                {name || 'Tag Name'}
              </span>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-lg p-2">
              {error}
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center gap-2 pt-2">
            <Button
              type="submit"
              disabled={!name.trim() || isSubmitting}
              size="sm"
              className="flex-1"
            >
              <Plus className="h-4 w-4 mr-1" />
              {isSubmitting ? 'Creating...' : 'Create Tag'}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleCancel}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </div>

      {/* Icon Picker Modal */}
      {showIconPicker && (
        <IconPicker
          selectedIcon={selectedIcon}
          onIconSelect={setSelectedIcon}
          onClose={() => setShowIconPicker(false)}
        />
      )}
    </>
  );
};

// Helper function to calculate text color based on background
function getTextColor(backgroundColor: string): string {
  const hex = backgroundColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.5 ? '#1f2937' : '#ffffff';
}

export default TagCreator;
