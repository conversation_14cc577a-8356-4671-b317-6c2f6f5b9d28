// Professional Timeline Task - Resizable & Draggable
import React, { useState, useCallback } from 'react';
import { useDraggable } from '@dnd-kit/core';
import { Clock, Tag as TagIcon, GripVertical } from 'lucide-react';
import { Task } from '@/services/api';
import { getTaskDurationHours } from './types';

interface ProfessionalTimelineTaskProps {
  task: Task;
  isCompact?: boolean;
  isExtended?: boolean;
  isPastTime?: boolean;
  slotWidth: number;
  zoom: number;
}

const ProfessionalTimelineTask: React.FC<ProfessionalTimelineTaskProps> = ({
  task,
  isCompact = false,
  isExtended = false,
  isPastTime = false
}) => {
  const [isResizing, setIsResizing] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
    data: { task }
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`
  } : undefined;

  const duration = getTaskDurationHours(task);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  const handleResizeStart = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setIsResizing(true);
  }, []);

  const handleResizeEnd = useCallback(() => {
    setIsResizing(false);
  }, []);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        relative group cursor-grab active:cursor-grabbing
        transition-all duration-200 select-none
        ${isDragging ? 'opacity-60 rotate-1 scale-105 z-50 shadow-2xl' : ''}
        ${isResizing ? 'cursor-ew-resize' : ''}
      `}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      data-draggable="true"
      {...attributes}
      {...listeners}
    >
      {/* Main Task Card */}
      <div
        className={`
          relative rounded-lg shadow-sm border-2 transition-all duration-200
          ${isPastTime
            ? 'bg-gray-100 border-gray-300 text-gray-600'
            : 'bg-white border-gray-200 text-gray-900 hover:border-gray-300 hover:shadow-md'
          }
          ${isExtended ? 'border-l-4 border-l-blue-500' : ''}
          ${isHovered ? 'shadow-lg' : ''}
          ${isCompact ? 'p-2' : 'p-3'}
        `}
      >
        {/* Task Header */}
        <div className="flex items-start justify-between gap-2">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {/* Drag Handle */}
            <GripVertical className={`
              h-4 w-4 flex-shrink-0 transition-opacity duration-200
              ${isHovered ? 'opacity-100' : 'opacity-0'}
              ${isPastTime ? 'text-gray-400' : 'text-gray-500'}
            `} />

            {/* Project Color Indicator */}
            {task.project && (
              <div
                className="w-3 h-3 rounded-full flex-shrink-0 shadow-sm"
                style={{ backgroundColor: task.project.color || '#64748b' }}
              />
            )}

            {/* Task Title */}
            <span className={`
              font-semibold truncate
              ${isCompact ? 'text-sm' : 'text-base'}
              ${isPastTime ? 'text-gray-600' : 'text-gray-900'}
            `}>
              {task.title}
            </span>
          </div>

          {/* Duration Indicator */}
          {isExtended && (
            <div className={`
              flex items-center gap-1 text-xs flex-shrink-0 px-2 py-1 rounded-full
              ${isPastTime ? 'text-gray-500 bg-gray-200' : 'text-blue-600 bg-blue-100'}
            `}>
              <Clock className="h-3 w-3" />
              <span className="font-medium">{duration}h</span>
            </div>
          )}
        </div>

        {/* Task Content */}
        {!isCompact && task.content && (
          <p className={`
            text-sm mt-2 line-clamp-2
            ${isPastTime ? 'text-gray-500' : 'text-gray-600'}
          `}>
            {task.content}
          </p>
        )}

        {/* Task Metadata */}
        <div className="flex items-center justify-between mt-3">
          {/* Tag */}
          {task.tag && (
            <div className="flex items-center gap-1">
              <TagIcon className={`h-3 w-3 ${isPastTime ? 'text-gray-400' : 'text-gray-400'}`} />
              <span
                className="text-xs px-2 py-1 rounded-full font-medium"
                style={{
                  backgroundColor: isPastTime ? '#f3f4f6' : `${task.tag.color}20`,
                  color: isPastTime ? '#6b7280' : task.tag.color
                }}
              >
                {task.tag.name}
              </span>
            </div>
          )}

          {/* Status Indicator */}
          <div className={`
            text-xs px-2 py-1 rounded-full font-medium
            ${getStatusStyles(task.status, isPastTime)}
          `}>
            {getStatusLabel(task.status)}
          </div>
        </div>

        {/* Resize Handle */}
        {isHovered && !isPastTime && (
          <div
            className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-2 h-8 bg-blue-500 rounded-full cursor-ew-resize opacity-80 hover:opacity-100 transition-opacity"
            onMouseDown={handleResizeStart}
            onMouseUp={handleResizeEnd}
          />
        )}

        {/* Extended Task Indicator */}
        {isExtended && (
          <div className="absolute -right-2 -top-2 w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-sm">
            <div className="w-full h-full bg-blue-500 rounded-full animate-pulse" />
          </div>
        )}

        {/* Past Time Overlay */}
        {isPastTime && (
          <div className="absolute inset-0 bg-gray-200 opacity-10 rounded-lg pointer-events-none" />
        )}
      </div>
    </div>
  );
};

// Helper function to get status styles
const getStatusStyles = (status: string, isPastTime: boolean = false): string => {
  if (isPastTime) {
    return 'bg-gray-200 text-gray-600';
  }

  switch (status) {
    case 'TODO':
      return 'bg-gray-100 text-gray-700';
    case 'IN_PROGRESS':
      return 'bg-blue-100 text-blue-700';
    case 'PAUSED':
      return 'bg-yellow-100 text-yellow-700';
    case 'DONE':
      return 'bg-green-100 text-green-700';
    case 'ARCHIVED':
      return 'bg-gray-100 text-gray-500';
    default:
      return 'bg-gray-100 text-gray-700';
  }
};

// Helper function to get status label
const getStatusLabel = (status: string): string => {
  switch (status) {
    case 'TODO':
      return 'To Do';
    case 'IN_PROGRESS':
      return 'In Progress';
    case 'PAUSED':
      return 'Paused';
    case 'DONE':
      return 'Done';
    case 'ARCHIVED':
      return 'Archived';
    default:
      return status;
  }
};

export default ProfessionalTimelineTask;
