// FIXED Timeline Task - Working Resize & Drag
import React, { useState, useCallback } from 'react';
import { useDraggable } from '@dnd-kit/core';
import { Clock, GripVertical } from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';

interface FixedTimelineTaskProps {
  task: Task;
  isCompact?: boolean;
  isExtended?: boolean;
  isPastTime?: boolean;
  duration: number;
  slotWidth: number;
}

const FixedTimelineTask: React.FC<FixedTimelineTaskProps> = ({
  task,
  isCompact = false,
  isExtended = false,
  isPastTime = false,
  duration,
  slotWidth
}) => {
  const [isResizing, setIsResizing] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const { updateTask } = useTaskStore();

  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
    data: { task }
  });

  // FIXED: Handle resize functionality
  const handleResizeStart = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setIsResizing(true);
  }, []);

  const handleResizeEnd = useCallback(() => {
    setIsResizing(false);
  }, []);

  const handleDurationChange = useCallback(async (newDuration: number) => {
    const clampedDuration = Math.max(0.25, Math.min(8, newDuration)); // 15min to 8hr
    await updateTask(task.id, {
      effortEstimate: clampedDuration * 60 // Convert to minutes
    });
  }, [task.id, updateTask]);

  // FIXED: Calculate task width for grid intersection
  const taskWidth = isExtended ? `${duration * slotWidth}px` : '100%';

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    width: taskWidth,
    zIndex: isDragging ? 1000 : 10
  } : {
    width: taskWidth
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        relative bg-white border border-gray-200 rounded-lg shadow-sm
        transition-all duration-200 cursor-pointer group
        ${isDragging ? 'opacity-50 shadow-lg scale-105' : ''}
        ${isPastTime ? 'opacity-60' : ''}
        ${isHovered ? 'shadow-md border-blue-300' : ''}
        ${isResizing ? 'cursor-ew-resize' : ''}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      data-draggable
    >
      {/* FIXED Task Content - Grid optimized */}
      <div
        className={`${isCompact ? 'p-1' : 'p-2'} flex items-center gap-1 h-full`}
        {...attributes}
        {...listeners}
      >
        {/* Drag Handle - smaller for grid */}
        {!isCompact && <GripVertical className="h-3 w-3 text-gray-400 flex-shrink-0" />}

        {/* Task Info */}
        <div className="flex-1 min-w-0">
          <div className={`${isCompact ? 'text-xs' : 'text-xs'} font-medium text-gray-900 truncate`}>
            {task.title}
          </div>

          {!isCompact && (
            <div className="flex items-center gap-1 mt-1">
              <Clock className="h-3 w-3 text-gray-400" />
              <span className="text-xs text-gray-500">
                {duration}h
              </span>

              {task.project && (
                <span
                  className="text-xs px-1 py-0.5 rounded text-white"
                  style={{ backgroundColor: task.project.color }}
                >
                  {task.project.name}
                </span>
              )}
            </div>
          )}
        </div>

        {/* FIXED Resize Handle */}
        {isExtended && (
          <div
            className={`
              absolute -right-1 top-0 bottom-0 w-2 cursor-ew-resize
              flex items-center justify-center
              ${isHovered ? 'bg-blue-500 opacity-50' : 'bg-transparent'}
              hover:bg-blue-500 hover:opacity-70
            `}
            onMouseDown={handleResizeStart}
            onMouseUp={handleResizeEnd}
          >
            <div className="w-1 h-4 bg-white rounded-full opacity-80" />
          </div>
        )}
      </div>

      {/* Duration Adjustment Controls */}
      {isHovered && !isDragging && (
        <div className="absolute -top-8 left-0 bg-white border border-gray-200 rounded shadow-lg p-1 flex items-center gap-1 z-50">
          <button
            onClick={() => handleDurationChange(duration - 0.25)}
            className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded"
            disabled={duration <= 0.25}
          >
            -15m
          </button>

          <span className="text-xs px-2 py-1 font-medium">
            {duration}h
          </span>

          <button
            onClick={() => handleDurationChange(duration + 0.25)}
            className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded"
            disabled={duration >= 8}
          >
            +15m
          </button>
        </div>
      )}

      {/* Status Indicator */}
      <div className={`
        absolute top-1 right-1 w-2 h-2 rounded-full
        ${task.status === 'IN_PROGRESS' ? 'bg-green-500' : ''}
        ${task.status === 'TODO' ? 'bg-blue-500' : ''}
        ${task.status === 'PAUSED' ? 'bg-yellow-500' : ''}
        ${task.status === 'DONE' ? 'bg-gray-500' : ''}
      `} />
    </div>
  );
};

export default FixedTimelineTask;
