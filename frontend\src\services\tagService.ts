import { apiService } from './api';

export interface Tag {
  id: string;
  name: string;
  icon: string;
  color: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  projectId: string;
  project?: {
    id: string;
    name: string;
    color: string;
  };
  _count?: {
    tasks: number;
  };
}

export interface CreateTagData {
  name: string;
  icon: string;
  color: string;
}

export interface UpdateTagData {
  name?: string;
  icon?: string;
  color?: string;
}

class TagService {
  /**
   * Get all tags for the current user
   */
  async getAllTags(): Promise<Tag[]> {
    return await apiService.getTags();
  }

  /**
   * Get all tags for a specific project
   */
  async getProjectTags(projectId: string): Promise<Tag[]> {
    return await apiService.getProjectTags(projectId);
  }

  /**
   * Create a new tag for a project
   */
  async createTag(projectId: string, tagData: CreateTagData): Promise<Tag> {
    return await apiService.createTag(projectId, tagData);
  }

  /**
   * Update an existing tag
   */
  async updateTag(tagId: string, tagData: UpdateTagData): Promise<Tag> {
    return await apiService.updateTag(tagId, tagData);
  }

  /**
   * Delete a tag
   */
  async deleteTag(tagId: string): Promise<void> {
    await apiService.deleteTag(tagId);
  }
}

export const tagService = new TagService();
