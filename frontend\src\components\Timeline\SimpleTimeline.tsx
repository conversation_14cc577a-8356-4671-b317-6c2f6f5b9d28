// SIMPLE Timeline - Exactly What You Asked For
import React, { useState } from 'react';
import { DndContext, DragEndEvent, useDraggable, useDroppable } from '@dnd-kit/core';
import { useTaskStore } from '@/store/taskStore';
import { Task } from '@/services/api';
import { ChevronLeft, ChevronRight, Edit2 } from 'lucide-react';

interface SimpleTimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

// Simple section
interface Section {
  id: string;
  name: string;
  color: string;
}

// Simple time slot
interface TimeSlot {
  id: string;
  hour: number;
  label: string;
}

// Simple task component with resize functionality
const SimpleTask: React.FC<{ task: Task; slot: TimeSlot; section: Section }> = ({ task, slot, section }) => {
  const { updateTask } = useTaskStore();
  const [isResizing, setIsResizing] = useState(false);
  const [taskWidth, setTaskWidth] = useState(128); // Default width (w-32 = 128px)

  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
  });

  // Calculate task duration in hours (default 1 hour)
  const duration = task.effortEstimate ? task.effortEstimate / 60 : 1;
  const calculatedWidth = Math.max(128, duration * 128); // 128px per hour, minimum 128px

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    width: `${calculatedWidth}px`,
  } : {
    width: `${calculatedWidth}px`,
  };

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent drag from starting
    setIsResizing(true);

    const startX = e.clientX;
    const startWidth = calculatedWidth;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX;
      const newWidth = Math.max(128, startWidth + deltaX); // Minimum 128px (1 hour)
      setTaskWidth(newWidth);
    };

    const handleMouseUp = async () => {
      setIsResizing(false);

      // Calculate new duration based on width (128px = 1 hour)
      const newDuration = Math.max(1, Math.round((taskWidth / 128) * 4) / 4); // Round to 15min increments
      const newEffortEstimate = newDuration * 60; // Convert to minutes

      // Update task duration
      await updateTask(task.id, {
        effortEstimate: newEffortEstimate
      });

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        relative p-2 bg-white border border-gray-200 rounded shadow-sm
        hover:shadow-md transition-all text-sm group
        ${isDragging ? 'opacity-50 rotate-2 scale-95' : ''}
        ${isResizing ? 'cursor-ew-resize' : 'cursor-grab'}
      `}
    >
      {/* Main task content - draggable area */}
      <div
        {...attributes}
        {...listeners}
        className="h-full"
      >
        <div className="font-medium truncate">{task.title}</div>
        {task.project && (
          <div className="text-xs text-gray-500 mt-1">{task.project.name}</div>
        )}
        <div className="text-xs text-gray-400 mt-1">
          {duration}h
        </div>
      </div>

      {/* Right resize handle - NOT draggable */}
      <div
        className={`
          absolute -right-1 top-0 bottom-0 w-2 cursor-ew-resize
          flex items-center justify-center opacity-0 group-hover:opacity-100
          hover:bg-blue-500 hover:opacity-70 transition-all
          ${isResizing ? 'bg-blue-500 opacity-70' : ''}
        `}
        onMouseDown={handleResizeStart}
        style={{ pointerEvents: 'auto' }} // Ensure resize handle is clickable
      >
        <div className="w-1 h-4 bg-white rounded-full opacity-80" />
      </div>

      {/* Left resize handle - NOT draggable */}
      <div
        className={`
          absolute -left-1 top-0 bottom-0 w-2 cursor-ew-resize
          flex items-center justify-center opacity-0 group-hover:opacity-100
          hover:bg-blue-500 hover:opacity-70 transition-all
          ${isResizing ? 'bg-blue-500 opacity-70' : ''}
        `}
        onMouseDown={(e) => {
          e.stopPropagation();
          // Handle left resize (shrink from left)
          setIsResizing(true);

          const startX = e.clientX;
          const startWidth = calculatedWidth;

          const handleMouseMove = (e: MouseEvent) => {
            const deltaX = startX - e.clientX; // Reverse direction for left handle
            const newWidth = Math.max(128, startWidth + deltaX);
            setTaskWidth(newWidth);
          };

          const handleMouseUp = async () => {
            setIsResizing(false);
            const newDuration = Math.max(1, Math.round((taskWidth / 128) * 4) / 4);
            const newEffortEstimate = newDuration * 60;

            await updateTask(task.id, {
              effortEstimate: newEffortEstimate
            });

            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
          };

          document.addEventListener('mousemove', handleMouseMove);
          document.addEventListener('mouseup', handleMouseUp);
        }}
        style={{ pointerEvents: 'auto' }}
      >
        <div className="w-1 h-4 bg-white rounded-full opacity-80" />
      </div>
    </div>
  );
};

// Simple slot component
const SimpleSlot: React.FC<{
  slot: TimeSlot;
  section: Section;
  tasks: Task[];
}> = ({ slot, section, tasks }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `${section.id}-${slot.id}`,
    data: { sectionId: section.id, slotId: slot.id, hour: slot.hour }
  });

  return (
    <div
      ref={setNodeRef}
      className={`
        border-r border-gray-200 p-2 min-h-[120px] w-32
        ${isOver ? 'bg-blue-50 border-blue-300' : 'hover:bg-gray-50'}
      `}
    >
      <div className="space-y-2">
        {tasks.map(task => (
          <SimpleTask key={task.id} task={task} slot={slot} section={section} />
        ))}
      </div>
      {isOver && tasks.length === 0 && (
        <div className="border-2 border-dashed border-blue-300 rounded p-4 text-center">
          <span className="text-xs text-blue-600">Drop here</span>
        </div>
      )}
    </div>
  );
};

const SimpleTimeline: React.FC<SimpleTimelineProps> = ({ currentDate, onDateChange }) => {
  const { tasks, updateTask } = useTaskStore();

  // Simple sections with editable names
  const [sections, setSections] = useState<Section[]>([
    { id: 'section-1', name: 'Work', color: '#3B82F6' },
    { id: 'section-2', name: 'Personal', color: '#10B981' },
    { id: 'section-3', name: 'Other', color: '#8B5CF6' },
    { id: 'section-4', name: 'Health', color: '#F59E0B' },
    { id: 'section-5', name: 'Learning', color: '#EF4444' }
  ]);

  const [sectionCount, setSectionCount] = useState(3);
  const [editingSection, setEditingSection] = useState<string | null>(null);

  // Simple time slots
  const timeSlots: TimeSlot[] = [];
  for (let hour = 6; hour <= 23; hour++) {
    timeSlots.push({
      id: `slot-${hour}`,
      hour,
      label: `${hour}:00`
    });
  }

  // Get tasks for specific slot - FIXED: Only show each task once
  const getTasksForSlot = (section: Section, slot: TimeSlot) => {
    const allTasksInSlot = tasks.filter(task => {
      if (!task.scheduledTime) return false;
      const taskDate = new Date(task.scheduledTime);
      return taskDate.toDateString() === currentDate.toDateString() &&
             taskDate.getHours() === slot.hour;
    });

    // Distribute tasks across sections - each task appears only once
    const sectionIndex = sections.findIndex(s => s.id === section.id);
    return allTasksInSlot.filter((_, taskIndex) =>
      taskIndex % sectionCount === sectionIndex
    );
  };

  // Get unscheduled tasks
  const unscheduledTasks = tasks.filter(task =>
    !task.scheduledTime && task.status === 'TODO'
  );

  // Handle drag end
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    const taskId = active.id as string;
    const dropData = over.data?.current;

    if (dropData?.slotId && dropData?.hour !== undefined) {
      const scheduledDate = new Date(currentDate);
      scheduledDate.setHours(dropData.hour, 0, 0, 0);

      await updateTask(taskId, {
        scheduledTime: scheduledDate.toISOString(),
        status: 'IN_PROGRESS' as const
      });
    }
  };

  // Edit section name
  const editSectionName = (sectionId: string, newName: string) => {
    setSections(prev => prev.map(section =>
      section.id === sectionId ? { ...section, name: newName } : section
    ));
    setEditingSection(null);
  };

  return (
    <DndContext onDragEnd={handleDragEnd}>
      <div className="h-screen flex flex-col bg-white">
        {/* Header */}
        <div className="border-b bg-gray-50 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => onDateChange(new Date(currentDate.getTime() - 24 * 60 * 60 * 1000))}
                className="p-2 hover:bg-gray-200 rounded"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <h2 className="text-lg font-semibold">
                {currentDate.toLocaleDateString()}
              </h2>
              <button
                onClick={() => onDateChange(new Date(currentDate.getTime() + 24 * 60 * 60 * 1000))}
                className="p-2 hover:bg-gray-200 rounded"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm">Sections:</span>
              {[1, 2, 3, 4, 5].map(count => (
                <button
                  key={count}
                  onClick={() => setSectionCount(count)}
                  className={`px-3 py-1 rounded ${
                    sectionCount === count
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 hover:bg-gray-300'
                  }`}
                >
                  {count}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Grid */}
        <div className="flex-1 overflow-auto">
          <div className="min-w-max">
            {/* Time header */}
            <div className="flex border-b bg-gray-50">
              <div className="w-48 p-3 border-r bg-white">
                <span className="font-medium">Sections</span>
              </div>
              {timeSlots.map(slot => (
                <div key={slot.id} className="w-32 p-3 text-center border-r border-gray-200">
                  <span className="text-sm font-medium">{slot.label}</span>
                </div>
              ))}
            </div>

            {/* Sections */}
            {sections.slice(0, sectionCount).map((section, index) => (
              <div key={section.id} className="flex border-b border-gray-200">
                {/* Section name */}
                <div className="w-48 p-3 bg-gray-50 border-r border-gray-200 flex items-center justify-between">
                  {editingSection === section.id ? (
                    <input
                      type="text"
                      value={section.name}
                      onChange={(e) => setSections(prev => prev.map(s =>
                        s.id === section.id ? { ...s, name: e.target.value } : s
                      ))}
                      onBlur={() => setEditingSection(null)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setEditingSection(null);
                        }
                      }}
                      className="bg-white border rounded px-2 py-1 text-sm w-full"
                      autoFocus
                    />
                  ) : (
                    <div className="flex items-center gap-2 w-full">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: section.color }}
                      />
                      <span className="font-medium text-sm flex-1">{section.name}</span>
                      <button
                        onClick={() => setEditingSection(section.id)}
                        className="p-1 hover:bg-gray-200 rounded"
                      >
                        <Edit2 className="h-3 w-3" />
                      </button>
                    </div>
                  )}
                </div>

                {/* Time slots */}
                {timeSlots.map(slot => (
                  <SimpleSlot
                    key={slot.id}
                    slot={slot}
                    section={section}
                    tasks={getTasksForSlot(section, slot)}
                  />
                ))}
              </div>
            ))}
          </div>
        </div>

        {/* Unscheduled tasks */}
        <div className="border-t bg-gray-50 p-4">
          <h3 className="font-medium mb-2">Unscheduled Tasks</h3>
          <div className="flex gap-2 flex-wrap">
            {unscheduledTasks.map(task => (
              <SimpleTask
                key={task.id}
                task={task}
                slot={{ id: 'unscheduled', hour: 0, label: 'Unscheduled' }}
                section={{ id: 'unscheduled', name: 'Unscheduled', color: '#6B7280' }}
              />
            ))}
          </div>
        </div>
      </div>
    </DndContext>
  );
};

export default SimpleTimeline;
