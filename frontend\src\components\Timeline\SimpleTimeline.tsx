// SIMPLE Timeline - Exactly What You Asked For
import React, { useState } from 'react';
import { DndContext, DragEndEvent, useDraggable, useDroppable } from '@dnd-kit/core';
import { useTaskStore } from '@/store/taskStore';
import { Task } from '@/services/api';
import { ChevronLeft, ChevronRight, Edit2 } from 'lucide-react';

interface SimpleTimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

// Simple section
interface Section {
  id: string;
  name: string;
  color: string;
}

// Simple time slot
interface TimeSlot {
  id: string;
  hour: number;
  label: string;
}

// Simple task component with resize functionality
const SimpleTask: React.FC<{ task: Task; slot: TimeSlot; section: Section }> = ({ task }) => {
  const { updateTask } = useTaskStore();
  const [isResizing, setIsResizing] = useState(false);
  const [taskWidth, setTaskWidth] = useState(128); // Default width (w-32 = 128px)

  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
  });

  // Calculate task duration in hours (default 1 hour)
  const duration = task.effortEstimate ? task.effortEstimate / 60 : 1;
  const calculatedWidth = Math.max(144, duration * 144); // 144px per hour (slot width), minimum 144px

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    width: `${calculatedWidth}px`,
    zIndex: 1000
  } : {
    width: `${calculatedWidth}px`,
    zIndex: 10
  };

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent drag from starting
    setIsResizing(true);

    const startX = e.clientX;
    const startWidth = calculatedWidth;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX;
      const newWidth = Math.max(144, startWidth + deltaX); // Minimum 144px (1 hour)
      setTaskWidth(newWidth);
    };

    const handleMouseUp = async () => {
      setIsResizing(false);

      // Calculate new duration based on width (144px = 1 hour)
      const newDuration = Math.max(1, Math.round((taskWidth / 144) * 4) / 4); // Round to 15min increments
      const newEffortEstimate = newDuration * 60; // Convert to minutes

      // Update task duration
      await updateTask(task.id, {
        effortEstimate: newEffortEstimate
      });

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        relative p-2 bg-white border border-gray-200 rounded shadow-sm
        hover:shadow-md transition-all text-sm group
        ${isDragging ? 'opacity-50 rotate-2 scale-95' : ''}
        ${isResizing ? 'cursor-ew-resize' : 'cursor-grab'}
      `}
    >
      {/* Main task content - draggable area */}
      <div
        {...attributes}
        {...listeners}
        className="h-full"
      >
        <div className="font-medium truncate">{task.title}</div>
        {task.project && (
          <div className="text-xs text-gray-500 mt-1">{task.project.name}</div>
        )}
        <div className="text-xs text-gray-400 mt-1">
          {duration}h
        </div>
      </div>

      {/* Right resize handle - NOT draggable */}
      <div
        className={`
          absolute -right-1 top-0 bottom-0 w-2 cursor-ew-resize
          flex items-center justify-center opacity-0 group-hover:opacity-100
          hover:bg-blue-500 hover:opacity-70 transition-all
          ${isResizing ? 'bg-blue-500 opacity-70' : ''}
        `}
        onMouseDown={handleResizeStart}
        style={{ pointerEvents: 'auto' }} // Ensure resize handle is clickable
      >
        <div className="w-1 h-4 bg-white rounded-full opacity-80" />
      </div>

      {/* Left resize handle - NOT draggable */}
      <div
        className={`
          absolute -left-1 top-0 bottom-0 w-2 cursor-ew-resize
          flex items-center justify-center opacity-0 group-hover:opacity-100
          hover:bg-blue-500 hover:opacity-70 transition-all
          ${isResizing ? 'bg-blue-500 opacity-70' : ''}
        `}
        onMouseDown={(e) => {
          e.stopPropagation();
          // Handle left resize (shrink from left)
          setIsResizing(true);

          const startX = e.clientX;
          const startWidth = calculatedWidth;

          const handleMouseMove = (e: MouseEvent) => {
            const deltaX = startX - e.clientX; // Reverse direction for left handle
            const newWidth = Math.max(144, startWidth + deltaX);
            setTaskWidth(newWidth);
          };

          const handleMouseUp = async () => {
            setIsResizing(false);
            const newDuration = Math.max(1, Math.round((taskWidth / 144) * 4) / 4);
            const newEffortEstimate = newDuration * 60;

            await updateTask(task.id, {
              effortEstimate: newEffortEstimate
            });

            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
          };

          document.addEventListener('mousemove', handleMouseMove);
          document.addEventListener('mouseup', handleMouseUp);
        }}
        style={{ pointerEvents: 'auto' }}
      >
        <div className="w-1 h-4 bg-white rounded-full opacity-80" />
      </div>
    </div>
  );
};

// FIXED: Simple slot component with overlap detection
const SimpleSlot: React.FC<{
  slot: TimeSlot;
  section: Section;
  tasks: Task[];
  allTasks: Task[];
  currentDate: Date;
}> = ({ slot, section, tasks, allTasks, currentDate }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `${section.id}-${slot.id}`,
    data: { sectionId: section.id, slotId: slot.id, hour: slot.hour }
  });

  // Check if this slot is occupied (for visual feedback)
  const isOccupied = tasks.length > 0;

  // Check if this is past time (for graying)
  const now = new Date();
  const isToday = currentDate.toDateString() === now.toDateString();
  const isPastTime = isToday && slot.hour < now.getHours();

  // Check if dropping would cause overlap
  const wouldOverlap = isOver && allTasks.some(task => {
    if (!task.scheduledTime) return false;

    const taskDate = new Date(task.scheduledTime);
    const taskSectionId = task.content || 'section-1';

    if (taskSectionId !== section.id ||
        taskDate.toDateString() !== currentDate.toDateString()) {
      return false;
    }

    const taskStartHour = taskDate.getHours();
    const taskDuration = task.effortEstimate ? task.effortEstimate / 60 : 1;
    const taskEndHour = taskStartHour + taskDuration;

    return slot.hour >= taskStartHour && slot.hour < taskEndHour;
  });

  return (
    <div
      ref={setNodeRef}
      className={`
        border-r border-gray-200 p-3 min-h-[140px] w-36 relative transition-all
        ${isPastTime ? 'bg-gray-100' : 'bg-white'}
        ${isOver && !wouldOverlap ? 'bg-blue-50 border-blue-300 shadow-inner' : ''}
        ${isOver && wouldOverlap ? 'bg-red-50 border-red-300 shadow-inner' : ''}
        ${!isOver && !isPastTime ? 'hover:bg-gray-50' : ''}
        ${isOccupied && !isPastTime ? 'bg-blue-25' : ''}
      `}
    >
      <div className="space-y-2">
        {tasks.map(task => {
          // Only show the task in its starting slot - task will span visually
          const taskDate = new Date(task.scheduledTime!);
          const taskStartHour = taskDate.getHours();
          const isStartingSlot = slot.hour === taskStartHour;

          return isStartingSlot ? (
            <SimpleTask key={task.id} task={task} slot={slot} section={section} />
          ) : null; // No extended indicators - task element will span visually
        })}
      </div>

      {/* Drop feedback */}
      {isOver && !wouldOverlap && tasks.length === 0 && (
        <div className="border-2 border-dashed border-blue-300 rounded p-4 text-center">
          <span className="text-xs text-blue-600">Drop here</span>
        </div>
      )}

      {/* Overlap warning */}
      {isOver && wouldOverlap && (
        <div className="border-2 border-dashed border-red-300 rounded p-4 text-center">
          <span className="text-xs text-red-600">Overlap!</span>
        </div>
      )}
    </div>
  );
};

const SimpleTimeline: React.FC<SimpleTimelineProps> = ({ currentDate, onDateChange }) => {
  const { tasks, updateTask } = useTaskStore();

  // Simple sections with editable names
  const [sections, setSections] = useState<Section[]>([
    { id: 'section-1', name: 'Work', color: '#3B82F6' },
    { id: 'section-2', name: 'Personal', color: '#10B981' },
    { id: 'section-3', name: 'Other', color: '#8B5CF6' },
    { id: 'section-4', name: 'Health', color: '#F59E0B' },
    { id: 'section-5', name: 'Learning', color: '#EF4444' }
  ]);

  const [sectionCount, setSectionCount] = useState(3);
  const [editingSection, setEditingSection] = useState<string | null>(null);

  // Mouse panning state
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({ x: 0, scrollLeft: 0 });
  const [isDraggingTask, setIsDraggingTask] = useState(false);
  const timelineRef = React.useRef<HTMLDivElement>(null);

  // Current time for indicators
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinutes = now.getMinutes();
  const isToday = currentDate.toDateString() === now.toDateString();

  // Simple time slots
  const timeSlots: TimeSlot[] = [];
  for (let hour = 6; hour <= 23; hour++) {
    timeSlots.push({
      id: `slot-${hour}`,
      hour,
      label: `${hour}:00`
    });
  }

  // FIXED: Get tasks for specific slot and section - NO AUTO-REDISTRIBUTION
  const getTasksForSlot = (section: Section, slot: TimeSlot) => {
    return tasks.filter(task => {
      if (!task.scheduledTime) return false;

      const taskDate = new Date(task.scheduledTime);
      const taskStartHour = taskDate.getHours();
      const duration = task.effortEstimate ? task.effortEstimate / 60 : 1;
      const taskEndHour = taskStartHour + duration;

      // Check if task is scheduled for today
      const isToday = taskDate.toDateString() === currentDate.toDateString();

      // Check if task belongs to this section (stored in task.content as section ID)
      const taskSectionId = task.content || 'section-1'; // Default to section-1 if no section stored
      const belongsToSection = taskSectionId === section.id;

      // Check if this time slot overlaps with the task's time span
      const slotOverlaps = slot.hour >= taskStartHour && slot.hour < taskEndHour;

      return isToday && belongsToSection && slotOverlaps;
    });
  };

  // Get tasks by status - NO AUTO-ARCHIVING
  const unscheduledTasks = tasks.filter(task =>
    !task.scheduledTime && task.status === 'TODO'
  );

  const pausedTasks = tasks.filter(task =>
    task.status === 'PAUSED'
  );

  const completedTasks = tasks.filter(task =>
    task.status === 'DONE'
  );

  const archivedTasks = tasks.filter(task =>
    task.status === 'ARCHIVED'
  );

  // Handle drag start - disable panning
  const handleDragStart = () => {
    setIsDraggingTask(true);
  };

  // FIXED: Handle drag end with section assignment and overlap detection
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    // Re-enable panning
    setIsDraggingTask(false);

    if (!over) return;

    const taskId = active.id as string;
    const dropData = over.data?.current;
    const task = tasks.find(t => t.id === taskId);

    if (!task || !dropData?.slotId || dropData?.hour === undefined || !dropData?.sectionId) {
      return;
    }

    const targetHour = dropData.hour;
    const targetSectionId = dropData.sectionId;
    const taskDuration = task.effortEstimate ? task.effortEstimate / 60 : 1;

    // COLLISION DETECTION: Check for overlapping tasks in the target section
    const hasOverlap = tasks.some(existingTask => {
      if (existingTask.id === taskId || !existingTask.scheduledTime) return false;

      const existingTaskDate = new Date(existingTask.scheduledTime);
      const existingTaskSectionId = existingTask.content || 'section-1';

      // Only check tasks in the same section and same day
      if (existingTaskSectionId !== targetSectionId ||
          existingTaskDate.toDateString() !== currentDate.toDateString()) {
        return false;
      }

      const existingStartHour = existingTaskDate.getHours();
      const existingDuration = existingTask.effortEstimate ? existingTask.effortEstimate / 60 : 1;
      const existingEndHour = existingStartHour + existingDuration;

      const newStartHour = targetHour;
      const newEndHour = targetHour + taskDuration;

      // Check if time ranges overlap
      return (newStartHour < existingEndHour && newEndHour > existingStartHour);
    });

    if (hasOverlap) {
      console.log('❌ COLLISION DETECTED: Cannot place task due to overlap');
      return; // Cancel the drop
    }

    // No overlap - proceed with placement
    const scheduledDate = new Date(currentDate);
    scheduledDate.setHours(targetHour, 0, 0, 0);

    await updateTask(taskId, {
      scheduledTime: scheduledDate.toISOString(),
      status: 'IN_PROGRESS' as const,
      content: targetSectionId // Store section ID in content field
    });

    console.log(`✅ Task placed in ${targetSectionId} at ${targetHour}:00`);
  };

  // Mouse panning handlers - DISABLED during task drag
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button !== 0 || isDraggingTask) return; // Only left mouse button, not during task drag
    setIsPanning(true);
    setPanStart({
      x: e.clientX,
      scrollLeft: timelineRef.current?.scrollLeft || 0
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isPanning || !timelineRef.current || isDraggingTask) return; // Don't pan during task drag
    e.preventDefault();
    const deltaX = e.clientX - panStart.x;
    timelineRef.current.scrollLeft = panStart.scrollLeft - deltaX;
  };

  const handleMouseUp = () => {
    if (!isDraggingTask) { // Only stop panning if not dragging task
      setIsPanning(false);
    }
  };

  const handleMouseLeave = () => {
    if (!isDraggingTask) { // Only stop panning if not dragging task
      setIsPanning(false);
    }
  };

  // Edit section name
  const editSectionName = (sectionId: string, newName: string) => {
    setSections(prev => prev.map(section =>
      section.id === sectionId ? { ...section, name: newName } : section
    ));
    setEditingSection(null);
  };

  return (
    <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
      <div className="h-screen flex flex-col bg-white overflow-hidden">

        {/* IMPROVED Header */}
        <div className="border-b bg-gradient-to-r from-blue-50 to-indigo-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <button
                onClick={() => onDateChange(new Date(currentDate.getTime() - 24 * 60 * 60 * 1000))}
                className="p-3 hover:bg-white/50 rounded-lg transition-colors shadow-sm"
              >
                <ChevronLeft className="h-5 w-5 text-gray-600" />
              </button>
              <div className="text-center">
                <h2 className="text-xl font-bold text-gray-800">
                  {currentDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </h2>
                <p className="text-sm text-gray-600 mt-1">Timeline View</p>
              </div>
              <button
                onClick={() => onDateChange(new Date(currentDate.getTime() + 24 * 60 * 60 * 1000))}
                className="p-3 hover:bg-white/50 rounded-lg transition-colors shadow-sm"
              >
                <ChevronRight className="h-5 w-5 text-gray-600" />
              </button>
            </div>

            <div className="flex items-center gap-4 bg-white/70 rounded-lg p-3 shadow-sm">
              <span className="text-sm font-medium text-gray-700">Sections:</span>
              <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map(count => (
                  <button
                    key={count}
                    onClick={() => setSectionCount(count)}
                    className={`px-4 py-2 rounded-md font-medium transition-all ${
                      sectionCount === count
                        ? 'bg-blue-500 text-white shadow-md'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {count}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* IMPROVED Grid - No scrollbar, mouse panning */}
        <div
          ref={timelineRef}
          className="overflow-x-auto overflow-y-hidden bg-gray-50"
          style={{
            height: 'calc(100vh - 160px - 224px)', // Screen - header - task zones (224px = h-56)
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            cursor: isPanning ? 'grabbing' : 'grab',
            WebkitScrollbar: { display: 'none' }
          } as React.CSSProperties}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
        >
          <div className="min-w-max bg-white shadow-sm relative">
            {/* IMPROVED Time header */}
            <div className="flex border-b-2 border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
              <div className="w-56 p-4 border-r-2 border-gray-200 bg-white shadow-sm">
                <span className="font-bold text-gray-800 text-lg">Sections</span>
                <p className="text-xs text-gray-500 mt-1">Drag tasks into time slots</p>
              </div>
              {timeSlots.map(slot => {
                const isPastTime = isToday && slot.hour < currentHour;
                const isCurrentTime = isToday && slot.hour === currentHour;

                return (
                  <div
                    key={slot.id}
                    className={`w-36 p-4 text-center border-r border-gray-200 relative ${
                      isPastTime ? 'bg-gray-200' :
                      isCurrentTime ? 'bg-blue-100' : 'bg-white/50'
                    }`}
                  >
                    <span className={`text-sm font-bold ${
                      isPastTime ? 'text-gray-500' :
                      isCurrentTime ? 'text-blue-700' : 'text-gray-700'
                    }`}>
                      {slot.label}
                    </span>
                    <div className={`text-xs mt-1 ${
                      isPastTime ? 'text-gray-400' :
                      isCurrentTime ? 'text-blue-600' : 'text-gray-500'
                    }`}>
                      {slot.hour < 12 ? 'AM' : 'PM'}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* RED CURRENT TIME LINE */}
            {isToday && (
              <div
                className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-50 pointer-events-none"
                style={{
                  left: `${224 + (currentHour - 6) * 144 + (currentMinutes / 60) * 144}px`
                }}
              >
                <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full"></div>
              </div>
            )}

            {/* Sections */}
            {sections.slice(0, sectionCount).map((section) => (
              <div key={section.id} className="flex border-b border-gray-200">
                {/* IMPROVED Section name */}
                <div className="w-56 p-4 bg-gradient-to-r from-white to-gray-50 border-r-2 border-gray-200 flex items-center justify-between shadow-sm">
                  {editingSection === section.id ? (
                    <input
                      type="text"
                      value={section.name}
                      onChange={(e) => setSections(prev => prev.map(s =>
                        s.id === section.id ? { ...s, name: e.target.value } : s
                      ))}
                      onBlur={() => setEditingSection(null)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          setEditingSection(null);
                        }
                      }}
                      className="bg-white border-2 border-blue-300 rounded-lg px-3 py-2 text-sm w-full font-medium"
                      autoFocus
                    />
                  ) : (
                    <div className="flex items-center gap-3 w-full">
                      <div
                        className="w-4 h-4 rounded-full shadow-sm border-2 border-white"
                        style={{ backgroundColor: section.color }}
                      />
                      <div className="flex-1">
                        <span className="font-bold text-gray-800 text-base">{section.name}</span>
                        <div className="text-xs text-gray-500 mt-1">
                          {tasks.filter(t => (t.content || 'section-1') === section.id && t.scheduledTime).length} tasks scheduled
                        </div>
                      </div>
                      <button
                        onClick={() => setEditingSection(section.id)}
                        className="p-2 hover:bg-blue-100 rounded-lg transition-colors"
                      >
                        <Edit2 className="h-4 w-4 text-gray-600" />
                      </button>
                    </div>
                  )}
                </div>

                {/* Time slots */}
                {timeSlots.map(slot => (
                  <SimpleSlot
                    key={slot.id}
                    slot={slot}
                    section={section}
                    tasks={getTasksForSlot(section, slot)}
                    allTasks={tasks}
                    currentDate={currentDate}
                  />
                ))}
              </div>
            ))}
          </div>
        </div>

        {/* TASK ZONES - ALWAYS VISIBLE */}
        <div className="h-56 border-t-4 border-blue-500 bg-blue-50 p-6 shadow-xl flex-shrink-0">
          <div className="grid grid-cols-4 gap-4 h-full">
            {/* Unscheduled Tasks */}
            <div className="bg-white rounded-lg p-4 shadow-lg border-2 border-blue-300 h-full flex flex-col">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                <h3 className="font-bold text-gray-800 text-base">Unscheduled</h3>
                <span className="text-sm bg-blue-500 text-white px-2 py-1 rounded-full font-medium">
                  {unscheduledTasks.length}
                </span>
              </div>
              <div className="space-y-1 flex-1 overflow-y-auto">
                {unscheduledTasks.map(task => (
                  <SimpleTask
                    key={task.id}
                    task={task}
                    slot={{ id: 'unscheduled', hour: 0, label: 'Unscheduled' }}
                    section={{ id: 'unscheduled', name: 'Unscheduled', color: '#3B82F6' }}
                  />
                ))}
              </div>
            </div>

            {/* Paused Tasks */}
            <div className="bg-yellow-50 rounded-lg p-3 shadow border border-yellow-200 h-full flex flex-col">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <h3 className="font-semibold text-gray-800 text-sm">Paused</h3>
                <span className="text-xs bg-yellow-500 text-white px-2 py-0.5 rounded-full">
                  {pausedTasks.length}
                </span>
              </div>
              <div className="space-y-1 flex-1 overflow-y-auto">
                {pausedTasks.map(task => (
                  <SimpleTask
                    key={task.id}
                    task={task}
                    slot={{ id: 'paused', hour: 0, label: 'Paused' }}
                    section={{ id: 'paused', name: 'Paused', color: '#EAB308' }}
                  />
                ))}
              </div>
            </div>

            {/* Completed Tasks */}
            <div className="bg-green-50 rounded-lg p-3 shadow border border-green-200 h-full flex flex-col">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <h3 className="font-semibold text-gray-800 text-sm">Completed</h3>
                <span className="text-xs bg-green-500 text-white px-2 py-0.5 rounded-full">
                  {completedTasks.length}
                </span>
              </div>
              <div className="space-y-1 flex-1 overflow-y-auto">
                {completedTasks.map(task => (
                  <SimpleTask
                    key={task.id}
                    task={task}
                    slot={{ id: 'completed', hour: 0, label: 'Completed' }}
                    section={{ id: 'completed', name: 'Completed', color: '#10B981' }}
                  />
                ))}
              </div>
            </div>

            {/* Archived Tasks */}
            <div className="bg-gray-100 rounded-lg p-3 shadow border border-gray-200 h-full flex flex-col">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                <h3 className="font-semibold text-gray-800 text-sm">Archived</h3>
                <span className="text-xs bg-gray-500 text-white px-2 py-0.5 rounded-full">
                  {archivedTasks.length}
                </span>
              </div>
              <div className="space-y-1 flex-1 overflow-y-auto">
                {archivedTasks.map(task => (
                  <SimpleTask
                    key={task.id}
                    task={task}
                    slot={{ id: 'archived', hour: 0, label: 'Archived' }}
                    section={{ id: 'archived', name: 'Archived', color: '#6B7280' }}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DndContext>
  );
};

export default SimpleTimeline;
