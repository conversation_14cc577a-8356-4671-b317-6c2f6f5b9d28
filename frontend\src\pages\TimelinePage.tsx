import React, { useState, useEffect } from 'react';
import { LayoutGrid, Clock } from 'lucide-react';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import Button from '@/components/ui/Button';
import SimpleTimeline from '@/components/Timeline/SimpleTimeline';
import DashboardPage from './DashboardPage';

type ViewMode = 'timeline' | 'dashboard';

const TimelinePage: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('timeline');
  const [currentDate, setCurrentDate] = useState(new Date());
  const { fetchTasks } = useTaskStore();
  const { fetchProjects } = useProjectStore();

  useEffect(() => {
    fetchTasks();
    fetchProjects();
  }, [fetchTasks, fetchProjects]);

  const handleDateChange = (date: Date) => {
    setCurrentDate(date);
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Header with View Toggle */}
      <div className="flex-shrink-0 bg-white border-b border-secondary-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-secondary-900">
              Time Management
            </h1>
            <p className="text-sm text-secondary-600 mt-1">
              Manage your tasks with powerful timeline and dashboard views
            </p>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2 bg-secondary-100 rounded-lg p-1">
            <Button
              variant={viewMode === 'timeline' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('timeline')}
              className="flex items-center space-x-2"
            >
              <Clock className="h-4 w-4" />
              <span>Timeline</span>
            </Button>
            <Button
              variant={viewMode === 'dashboard' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('dashboard')}
              className="flex items-center space-x-2"
            >
              <LayoutGrid className="h-4 w-4" />
              <span>Dashboard</span>
            </Button>
          </div>
        </div>

        {/* View Description */}
        <div className="mt-3 text-xs text-secondary-500">
          {viewMode === 'timeline' ? (
            <div className="flex items-center space-x-4">
              <span>📊 Grid timeline with Y-axis project/tag organization</span>
              <span>🎯 Drag tasks between sections and time slots</span>
              <span>⚡ 1-5 configurable sections with adaptive naming</span>
            </div>
          ) : (
            <div className="flex items-center space-x-4">
              <span>📋 Vertical time slots for detailed day planning</span>
              <span>🗓️ Navigate between dates for future scheduling</span>
              <span>🎨 Visual time periods with drag-and-drop</span>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'timeline' ? (
          <SimpleTimeline
            currentDate={currentDate}
            onDateChange={handleDateChange}
          />
        ) : (
          <div className="h-full overflow-auto">
            <DashboardPage />
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="flex-shrink-0 bg-secondary-50 border-t border-secondary-200 px-6 py-2">
        <div className="flex items-center justify-between text-xs text-secondary-600">
          <div className="flex items-center space-x-4">
            <span>Current View: {viewMode === 'timeline' ? 'Timeline' : 'Dashboard'}</span>
            <span>Date: {currentDate.toLocaleDateString()}</span>
          </div>
          <div className="flex items-center space-x-4">
            <span>💡 Tip: Use drag-and-drop to schedule tasks efficiently</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimelinePage;
