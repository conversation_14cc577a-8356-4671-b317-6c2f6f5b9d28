// FIXED Timeline - Clean & Working
import React, { useState, useRef, useCallback } from 'react';
import { DndContext } from '@dnd-kit/core';
import { useTimelineState } from './hooks/useTimelineState';
import { useTaskDrop } from './hooks/useTaskDrop';
import TimelineHeader from './TimelineHeader';
import FixedTimelineGrid from './FixedTimelineGrid';
import SeamlessTaskZones from './SeamlessTaskZones';

interface GridTimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

const GridTimeline: React.FC<GridTimelineProps> = ({ currentDate, onDateChange }) => {
  const [scrollOffset, setScrollOffset] = useState(0);
  const timelineRef = useRef<HTMLDivElement>(null);

  // Clean state management with custom hooks
  const {
    tasks,
    timeSlots,
    gridConfig,
    setGridConfig,
    filteredTasks,
    sections,
    unscheduledTasks,
    pausedTasks,
    completedTasks,
    archivedTasks
  } = useTimelineState(currentDate);

  // Clean drag and drop logic
  const {
    draggedTask,
    handleDragStart,
    handleDragEnd,
    sensors
  } = useTaskDrop(tasks, currentDate);

  // FIXED: Simple horizontal scroll panning
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollOffset(e.currentTarget.scrollLeft);
  }, []);

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-screen flex flex-col bg-white overflow-hidden">
        {/* Clean Header */}
        <TimelineHeader
          currentDate={currentDate}
          onDateChange={onDateChange}
          gridConfig={gridConfig}
          onGridConfigChange={setGridConfig}
        />

        {/* FIXED Timeline Grid - Simple horizontal scroll */}
        <div
          ref={timelineRef}
          className="flex-1 overflow-x-auto overflow-y-hidden"
          onScroll={handleScroll}
        >
          <FixedTimelineGrid
            timeSlots={timeSlots}
            tasks={filteredTasks}
            gridConfig={gridConfig}
            sections={sections}
            draggedTask={draggedTask}
            scrollOffset={scrollOffset}
          />
        </div>

        {/* Task Zones */}
        <SeamlessTaskZones
          unscheduledTasks={unscheduledTasks}
          pausedTasks={pausedTasks}
          completedTasks={completedTasks}
          archivedTasks={archivedTasks}
        />
      </div>
    </DndContext>
  );
};

export default GridTimeline;
