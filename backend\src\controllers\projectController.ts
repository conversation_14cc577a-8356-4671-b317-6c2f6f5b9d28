import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import {
  createProjectSchema,
  updateProjectSchema,
  idParamSchema,
  paginationSchema
} from '../config/validation';

const prisma = new PrismaClient();

export class ProjectController {
  /**
   * Create a new project
   */
  static async createProject(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      // Validate input
      const { name, color } = createProjectSchema.parse(req.body);

      // Create project
      const project = await prisma.project.create({
        data: {
          name,
          color,
          userId: req.user.id,
        },
        include: {
          _count: {
            select: {
              tasks: true,
              tags: true,
            },
          },
        },
      });

      res.status(201).json({
        status: 'success',
        message: 'Project created successfully',
        data: {
          project,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all projects for current user
   */
  static async getProjects(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      // Parse pagination
      const { page = 1, limit = 50 } = paginationSchema.parse(req.query);
      const skip = (page - 1) * limit;

      // Get projects with task counts
      const [projects, total] = await Promise.all([
        prisma.project.findMany({
          where: {
            userId: req.user.id,
          },
          include: {
            _count: {
              select: {
                tasks: {
                  where: {
                    status: {
                      not: 'ARCHIVED',
                    },
                  },
                },
                tags: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          skip,
          take: limit,
        }),
        prisma.project.count({
          where: {
            userId: req.user.id,
          },
        }),
      ]);

      res.json({
        status: 'success',
        data: {
          projects,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get a specific project by ID
   */
  static async getProjectById(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const { id } = idParamSchema.parse(req.params);

      const project = await prisma.project.findFirst({
        where: {
          id,
          userId: req.user.id,
        },
        include: {
          tasks: {
            where: {
              status: {
                not: 'ARCHIVED',
              },
            },
            include: {
              tag: {
                select: {
                  id: true,
                  name: true,
                  icon: true,
                  color: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
          tags: {
            include: {
              _count: {
                select: {
                  tasks: true,
                },
              },
            },
            orderBy: {
              createdAt: 'asc',
            },
          },
          _count: {
            select: {
              tasks: true,
              tags: true,
            },
          },
        },
      });

      if (!project) {
        return res.status(404).json({
          status: 'error',
          message: 'Project not found',
        });
      }

      res.json({
        status: 'success',
        data: {
          project,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a project
   */
  static async updateProject(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const { id } = idParamSchema.parse(req.params);
      const updateData = updateProjectSchema.parse(req.body);

      // Check if project exists and belongs to user
      const existingProject = await prisma.project.findFirst({
        where: {
          id,
          userId: req.user.id,
        },
      });

      if (!existingProject) {
        return res.status(404).json({
          status: 'error',
          message: 'Project not found',
        });
      }

      // Update project
      const project = await prisma.project.update({
        where: { id },
        data: updateData,
        include: {
          _count: {
            select: {
              tasks: true,
              tags: true,
            },
          },
        },
      });

      res.json({
        status: 'success',
        message: 'Project updated successfully',
        data: {
          project,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a project
   */
  static async deleteProject(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const { id } = idParamSchema.parse(req.params);

      // Check if project exists and belongs to user
      const existingProject = await prisma.project.findFirst({
        where: {
          id,
          userId: req.user.id,
        },
        include: {
          _count: {
            select: {
              tasks: true,
              tags: true,
            },
          },
        },
      });

      if (!existingProject) {
        return res.status(404).json({
          status: 'error',
          message: 'Project not found',
        });
      }

      // Delete project (tasks will be set to null due to SetNull cascade)
      await prisma.project.delete({
        where: { id },
      });

      res.json({
        status: 'success',
        message: 'Project deleted successfully',
        data: {
          deletedProject: existingProject,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
