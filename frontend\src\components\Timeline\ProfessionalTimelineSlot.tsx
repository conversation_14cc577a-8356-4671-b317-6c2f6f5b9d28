// Professional Timeline Slot - Seamless Interactions
import React, { useState, useCallback } from 'react';
import { useDroppable } from '@dnd-kit/core';
import { Plus } from 'lucide-react';
import { Task } from '@/services/api';
import { TimeSlot, YAxisSection, getTaskDurationHours } from './types';
import ProfessionalTimelineTask from './ProfessionalTimelineTask';

interface ProfessionalTimelineSlotProps {
  slot: TimeSlot;
  section: YAxisSection;
  tasks: Task[];
  slotWidth: number;
  height: number;
  isCurrentTime?: boolean;
  zoom: number;
}

const ProfessionalTimelineSlot: React.FC<ProfessionalTimelineSlotProps> = ({
  slot,
  section,
  tasks,
  slotWidth,
  height,
  zoom
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  const { setNodeRef, isOver } = useDroppable({
    id: `${section.id}-${slot.id}`,
    data: { 
      sectionId: section.id, 
      slotId: slot.id,
      hour: slot.hour 
    }
  });

  // Get current time for styling
  const now = new Date();
  const currentHour = now.getHours();
  const isPastTime = slot.hour < currentHour;
  const isCurrentHour = slot.hour === currentHour;

  // Filter tasks that start in this slot
  const slotTasks = tasks.filter(task => {
    if (!task.scheduledTime) return false;
    const taskDate = new Date(task.scheduledTime);
    return taskDate.getHours() === slot.hour && 
           taskDate.toDateString() === slot.date.toDateString();
  });

  // Get tasks that extend into this slot from previous slots
  const extendedTasks = tasks.filter(task => {
    if (!task.scheduledTime) return false;
    const taskDate = new Date(task.scheduledTime);
    const taskStartHour = taskDate.getHours();
    const taskDuration = getTaskDurationHours(task);
    const taskEndHour = taskStartHour + taskDuration;
    
    return taskStartHour < slot.hour && taskEndHour > slot.hour &&
           taskDate.toDateString() === slot.date.toDateString();
  });

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  return (
    <div
      ref={setNodeRef}
      className={`
        border-r border-gray-200 relative transition-all duration-200
        ${isPastTime ? 'bg-gray-50' : 'bg-white'}
        ${isCurrentHour ? 'bg-blue-50 border-blue-200' : ''}
        ${isOver ? 'bg-blue-100 border-blue-300 shadow-inner' : ''}
        ${isHovered && !isPastTime ? 'bg-gray-50' : ''}
      `}
      style={{ 
        width: `${slotWidth}px`, 
        minWidth: `${slotWidth}px`,
        height: `${height}px`
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Current Time Indicator */}
      {isCurrentHour && (
        <div className="absolute top-0 left-0 right-0 h-1 bg-blue-500 z-20" />
      )}

      {/* Slot Content */}
      <div className="p-3 h-full flex flex-col relative">
        {/* Tasks that start in this slot */}
        <div className="space-y-2 flex-1 relative z-10">
          {slotTasks.map((task) => {
            const duration = getTaskDurationHours(task);
            const taskWidth = duration > 1 ? duration * slotWidth : slotWidth - 24;
            
            return (
              <div
                key={task.id}
                className="relative"
                style={{
                  width: duration > 1 ? `${taskWidth}px` : '100%',
                  zIndex: 15
                }}
              >
                <ProfessionalTimelineTask
                  task={task}
                  isCompact={slotTasks.length > 2}
                  isExtended={duration > 1}
                  isPastTime={isPastTime}
                  slotWidth={slotWidth}
                  zoom={zoom}
                />
              </div>
            );
          })}
        </div>

        {/* Extended task indicators (from previous slots) */}
        {extendedTasks.map(task => (
          <div
            key={`extended-${task.id}`}
            className="absolute top-3 left-3 right-3 h-8 bg-gray-200 border border-gray-300 rounded-lg opacity-60 z-5 flex items-center px-2"
          >
            <span className="text-xs text-gray-600 truncate">
              {task.title} (continued)
            </span>
          </div>
        ))}

        {/* Professional Drop Indicator */}
        {isOver && (
          <div className="absolute inset-3 border-2 border-dashed border-blue-400 rounded-lg bg-blue-50 flex items-center justify-center z-20">
            <div className="text-center">
              <Plus className="h-6 w-6 text-blue-600 mx-auto mb-2" />
              <span className="text-sm text-blue-600 font-medium">
                Drop task here
              </span>
            </div>
          </div>
        )}

        {/* Empty State with Time Label */}
        {slotTasks.length === 0 && extendedTasks.length === 0 && !isOver && (
          <div className={`
            flex-1 flex items-center justify-center transition-opacity duration-200
            ${isHovered ? 'opacity-100' : 'opacity-0'}
          `}>
            <div className="text-center">
              <Plus className="h-4 w-4 text-gray-300 mx-auto mb-1" />
              <span className="text-xs text-gray-400">
                {slot.hour}:00
              </span>
            </div>
          </div>
        )}

        {/* Task Overflow Indicator */}
        {slotTasks.length > 3 && (
          <div className="absolute bottom-3 right-3 z-20">
            <span className="text-xs text-gray-700 bg-white px-2 py-1 rounded-full border shadow-sm font-medium">
              +{slotTasks.length - 3}
            </span>
          </div>
        )}
      </div>

      {/* Past Time Overlay */}
      {isPastTime && (
        <div className="absolute inset-0 bg-gray-300 opacity-10 pointer-events-none z-5" />
      )}

      {/* Hover Effect */}
      {isHovered && !isPastTime && !isOver && (
        <div className="absolute inset-0 bg-blue-50 opacity-30 pointer-events-none z-5" />
      )}
    </div>
  );
};

export default ProfessionalTimelineSlot;
