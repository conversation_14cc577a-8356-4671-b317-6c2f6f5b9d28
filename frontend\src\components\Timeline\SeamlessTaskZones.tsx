// Seamless Task Zones - Professional Design
import React, { useState } from 'react';
import { useDroppable, useDraggable } from '@dnd-kit/core';
import { 
  Clock, 
  Pause, 
  CheckCircle, 
  Archive, 
  ChevronDown, 
  ChevronRight,
  Plus
} from 'lucide-react';
import { Task } from '@/services/api';

interface SeamlessTaskZonesProps {
  unscheduledTasks: Task[];
  pausedTasks: Task[];
  completedTasks: Task[];
  archivedTasks: Task[];
}

const SeamlessTaskZones: React.FC<SeamlessTaskZonesProps> = ({
  unscheduledTasks,
  pausedTasks,
  completedTasks,
  archivedTasks
}) => {
  return (
    <div className="h-48 bg-gradient-to-r from-gray-50 to-white border-t border-gray-200 p-4">
      <div className="grid grid-cols-4 gap-4 h-full">
        <SeamlessTaskZone
          id="unscheduled-section"
          title="Unscheduled"
          icon={<Clock className="h-4 w-4" />}
          tasks={unscheduledTasks}
          color="blue"
          description="Drag tasks to timeline"
          defaultExpanded={true}
        />

        <SeamlessTaskZone
          id="paused-section"
          title="Paused"
          icon={<Pause className="h-4 w-4" />}
          tasks={pausedTasks}
          color="yellow"
          description="Temporarily paused"
          defaultExpanded={false}
        />

        <SeamlessTaskZone
          id="completed-section"
          title="Completed"
          icon={<CheckCircle className="h-4 w-4" />}
          tasks={completedTasks}
          color="green"
          description="Finished tasks"
          defaultExpanded={false}
        />

        <SeamlessTaskZone
          id="archived-section"
          title="Archived"
          icon={<Archive className="h-4 w-4" />}
          tasks={archivedTasks}
          color="gray"
          description="Archived tasks"
          defaultExpanded={false}
        />
      </div>
    </div>
  );
};

// Seamless Task Zone Component
interface SeamlessTaskZoneProps {
  id: string;
  title: string;
  icon: React.ReactNode;
  tasks: Task[];
  color: 'blue' | 'yellow' | 'green' | 'gray';
  description: string;
  defaultExpanded: boolean;
}

const SeamlessTaskZone: React.FC<SeamlessTaskZoneProps> = ({
  id,
  title,
  icon,
  tasks,
  color,
  description,
  defaultExpanded
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  
  const { setNodeRef, isOver } = useDroppable({ id });

  const colorClasses = {
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      text: 'text-blue-700',
      drop: 'bg-blue-100 border-blue-300'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-700',
      drop: 'bg-yellow-100 border-yellow-300'
    },
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      text: 'text-green-700',
      drop: 'bg-green-100 border-green-300'
    },
    gray: {
      bg: 'bg-gray-50',
      border: 'border-gray-200',
      text: 'text-gray-700',
      drop: 'bg-gray-100 border-gray-300'
    }
  }[color];

  return (
    <div className="flex flex-col h-full">
      {/* Zone Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`
          flex items-center justify-between p-3 rounded-lg border-2 transition-all
          ${colorClasses.bg} ${colorClasses.border} hover:shadow-sm
        `}
      >
        <div className="flex items-center gap-2">
          <div className={colorClasses.text}>
            {icon}
          </div>
          <div className="text-left">
            <h3 className={`font-semibold text-sm ${colorClasses.text}`}>
              {title}
            </h3>
            <p className="text-xs text-gray-500">
              {tasks.length} tasks
            </p>
          </div>
        </div>
        
        <div className={`transition-transform ${colorClasses.text}`}>
          {isExpanded ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </div>
      </button>

      {/* Zone Content */}
      {isExpanded && (
        <div
          ref={setNodeRef}
          className={`
            flex-1 mt-2 p-3 rounded-lg border-2 transition-all
            ${isOver ? colorClasses.drop : `${colorClasses.bg} ${colorClasses.border}`}
          `}
        >
          {/* Drop Indicator */}
          {isOver && tasks.length === 0 && (
            <div className={`p-4 border-2 border-dashed rounded-lg ${colorClasses.border}`}>
              <div className="text-center">
                <Plus className={`h-6 w-6 mx-auto mb-2 ${colorClasses.text}`} />
                <p className={`text-sm font-medium ${colorClasses.text}`}>
                  Drop task here
                </p>
              </div>
            </div>
          )}

          {/* Tasks */}
          <div className="space-y-2 max-h-24 overflow-y-auto">
            {tasks.map(task => (
              <SeamlessZoneTask key={task.id} task={task} />
            ))}
          </div>

          {/* Empty State */}
          {tasks.length === 0 && !isOver && (
            <div className="text-center py-4">
              <p className="text-sm text-gray-500">{description}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Seamless Zone Task Component
interface SeamlessZoneTaskProps {
  task: Task;
}

const SeamlessZoneTask: React.FC<SeamlessZoneTaskProps> = ({ task }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
    data: { task }
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        p-2 bg-white border border-gray-200 rounded-lg cursor-grab active:cursor-grabbing
        hover:shadow-sm transition-all select-none
        ${isDragging ? 'opacity-60 rotate-1 scale-95 shadow-lg' : ''}
      `}
      data-draggable="true"
      {...attributes}
      {...listeners}
    >
      <div className="flex items-center gap-2">
        {task.project && (
          <div
            className="w-2 h-2 rounded-full flex-shrink-0"
            style={{ backgroundColor: task.project.color || '#64748b' }}
          />
        )}
        <span className="text-sm font-medium text-gray-900 truncate">
          {task.title}
        </span>
      </div>
      {task.tag && (
        <div className="mt-1">
          <span
            className="text-xs px-2 py-1 rounded-full font-medium"
            style={{
              backgroundColor: `${task.tag.color}20`,
              color: task.tag.color
            }}
          >
            {task.tag.name}
          </span>
        </div>
      )}
    </div>
  );
};

export default SeamlessTaskZones;
