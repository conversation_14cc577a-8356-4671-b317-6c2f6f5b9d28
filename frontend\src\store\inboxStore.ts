import { create } from 'zustand';
import { apiService, InboxItem, Task } from '@/services/api';

interface InboxState {
  inboxItems: InboxItem[];
  isLoading: boolean;
  error: string | null;
}

interface InboxActions {
  fetchInboxItems: () => Promise<void>;
  addInboxItem: (content: string) => Promise<InboxItem>;
  processInboxItem: (id: string, action: 'convert_to_task' | 'delete' | 'defer', taskData?: {
    title: string;
    content?: string;
    tagId?: string;
    dueDate?: string;
    effortEstimate?: number;
    projectId?: string;
  }) => Promise<{ task?: Task }>;
  deleteInboxItem: (id: string) => Promise<void>;
  clearError: () => void;
}

type InboxStore = InboxState & InboxActions;

export const useInboxStore = create<InboxStore>((set) => ({
  // Initial state
  inboxItems: [],
  isLoading: false,
  error: null,

  // Actions
  fetchInboxItems: async () => {
    try {
      set({ isLoading: true, error: null });

      const inboxItems = await apiService.getInboxItems();

      set({
        inboxItems,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch inbox items';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  addInboxItem: async (content: string) => {
    try {
      set({ isLoading: true, error: null });

      const newInboxItem = await apiService.addInboxItem(content);

      set((state) => ({
        inboxItems: [newInboxItem, ...state.inboxItems],
        isLoading: false,
        error: null,
      }));

      return newInboxItem;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to add inbox item';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  processInboxItem: async (id: string, action: 'convert_to_task' | 'delete' | 'defer', taskData?) => {
    try {
      set({ isLoading: true, error: null });

      const result = await apiService.processInboxItem(id, action, taskData);

      // Remove the processed item from the inbox
      set((state) => ({
        inboxItems: state.inboxItems.filter((item) => item.id !== id),
        isLoading: false,
        error: null,
      }));

      return result;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to process inbox item';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  deleteInboxItem: async (id: string) => {
    try {
      set({ isLoading: true, error: null });

      await apiService.deleteInboxItem(id);

      set((state) => ({
        inboxItems: state.inboxItems.filter((item) => item.id !== id),
        isLoading: false,
        error: null,
      }));
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to delete inbox item';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  clearError: () => {
    set({ error: null });
  },
}));
