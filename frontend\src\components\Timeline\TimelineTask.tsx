// Clean Timeline Task Component
import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { Clock, Tag as TagIcon } from 'lucide-react';
import { Task } from '@/services/api';
import { getTaskDurationHours } from './types';

interface TimelineTaskProps {
  task: Task;
  isCompact?: boolean;
  isExtended?: boolean;
  isPastTime?: boolean;
}

const TimelineTask: React.FC<TimelineTaskProps> = ({
  task,
  isCompact = false,
  isExtended = false,
  isPastTime = false
}) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`
  } : undefined;

  const duration = getTaskDurationHours(task);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        relative border rounded-lg shadow-sm cursor-grab
        hover:shadow-md transition-all select-none
        ${isDragging ? 'opacity-50 rotate-2 scale-95 z-50' : ''}
        ${isCompact ? 'p-2' : 'p-3'}
        ${isPastTime
          ? 'bg-gray-100 border-gray-300 text-gray-600'
          : 'bg-white border-gray-200 text-gray-900 hover:border-gray-300'
        }
        ${isExtended ? 'border-l-4 border-l-blue-500' : ''}
      `}
      {...attributes}
      {...listeners}
    >
      {/* Task Header */}
      <div className="flex items-start justify-between gap-2">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {/* Project Color Indicator */}
          {task.project && (
            <div
              className="w-2 h-2 rounded-full flex-shrink-0"
              style={{ backgroundColor: task.project.color || '#64748b' }}
            />
          )}

          {/* Task Title */}
          <span className={`
            font-medium truncate
            ${isCompact ? 'text-xs' : 'text-sm'}
            ${isPastTime ? 'text-gray-600' : 'text-gray-900'}
          `}>
            {task.title}
          </span>
        </div>

        {/* Duration Indicator */}
        {isExtended && (
          <div className={`
            flex items-center gap-1 text-xs flex-shrink-0
            ${isPastTime ? 'text-gray-500' : 'text-gray-600'}
          `}>
            <Clock className="h-3 w-3" />
            <span>{duration}h</span>
          </div>
        )}
      </div>

      {/* Task Content */}
      {!isCompact && task.content && (
        <p className={`
          text-xs mt-1 line-clamp-2
          ${isPastTime ? 'text-gray-500' : 'text-gray-600'}
        `}>
          {task.content}
        </p>
      )}

      {/* Task Metadata */}
      <div className="flex items-center justify-between mt-2">
        {/* Tag */}
        {task.tag && (
          <div className="flex items-center gap-1">
            <TagIcon className={`h-3 w-3 ${isPastTime ? 'text-gray-400' : 'text-gray-400'}`} />
            <span
              className="text-xs px-2 py-1 rounded-full"
              style={{
                backgroundColor: isPastTime ? '#f3f4f6' : `${task.tag.color}20`,
                color: isPastTime ? '#6b7280' : task.tag.color
              }}
            >
              {task.tag.name}
            </span>
          </div>
        )}

        {/* Status Indicator */}
        <div className={`
          text-xs px-2 py-1 rounded-full font-medium
          ${getStatusStyles(task.status, isPastTime)}
        `}>
          {getStatusLabel(task.status)}
        </div>
      </div>

      {/* Extended Task Indicator */}
      {isExtended && (
        <div className="absolute -right-1 -top-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white">
          <div className="w-full h-full bg-blue-500 rounded-full animate-pulse" />
        </div>
      )}

      {/* Past Time Overlay */}
      {isPastTime && (
        <div className="absolute inset-0 bg-gray-200 opacity-10 rounded-lg pointer-events-none" />
      )}

      {/* Drag Handle Visual */}
      <div className="absolute inset-0 rounded-lg border-2 border-transparent group-hover:border-blue-200 pointer-events-none" />
    </div>
  );
};

// Helper function to get status styles
const getStatusStyles = (status: string, isPastTime: boolean = false): string => {
  if (isPastTime) {
    return 'bg-gray-200 text-gray-600';
  }

  switch (status) {
    case 'TODO':
      return 'bg-gray-100 text-gray-700';
    case 'IN_PROGRESS':
      return 'bg-blue-100 text-blue-700';
    case 'PAUSED':
      return 'bg-yellow-100 text-yellow-700';
    case 'DONE':
      return 'bg-green-100 text-green-700';
    case 'ARCHIVED':
      return 'bg-gray-100 text-gray-500';
    default:
      return 'bg-gray-100 text-gray-700';
  }
};

// Helper function to get status label
const getStatusLabel = (status: string): string => {
  switch (status) {
    case 'TODO':
      return 'To Do';
    case 'IN_PROGRESS':
      return 'Active';
    case 'PAUSED':
      return 'Paused';
    case 'DONE':
      return 'Done';
    case 'ARCHIVED':
      return 'Archived';
    default:
      return status;
  }
};

export default TimelineTask;
