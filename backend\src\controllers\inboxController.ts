import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import {
  createInboxItemSchema,
  processInboxItemSchema,
  idParamSchema,
  paginationSchema
} from '../config/validation';

const prisma = new PrismaClient();

export class InboxController {
  /**
   * Add item to inbox (Universal Quick Add)
   */
  static async addInboxItem(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      // Validate input
      const { content } = createInboxItemSchema.parse(req.body);

      // Create inbox item
      const inboxItem = await prisma.inboxItem.create({
        data: {
          content,
          userId: req.user.id,
        },
      });

      res.status(201).json({
        status: 'success',
        message: 'Item added to inbox successfully',
        data: {
          inboxItem,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all inbox items for current user
   */
  static async getInboxItems(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      // Parse pagination
      const { page = 1, limit = 50 } = paginationSchema.parse(req.query);
      const skip = (page - 1) * limit;

      // Get inbox items
      const [inboxItems, total] = await Promise.all([
        prisma.inboxItem.findMany({
          where: {
            userId: req.user.id,
            status: 'UNPROCESSED', // Only show unprocessed items by default
          },
          orderBy: {
            createdAt: 'desc',
          },
          skip,
          take: limit,
        }),
        prisma.inboxItem.count({
          where: {
            userId: req.user.id,
            status: 'UNPROCESSED',
          },
        }),
      ]);

      res.json({
        status: 'success',
        data: {
          inboxItems,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Process an inbox item (convert to task, delete, or defer)
   */
  static async processInboxItem(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      // Validate params and body
      const { id } = idParamSchema.parse(req.params);
      const { action, taskData } = processInboxItemSchema.parse(req.body);

      // Find inbox item
      const inboxItem = await prisma.inboxItem.findFirst({
        where: {
          id,
          userId: req.user.id,
        },
      });

      if (!inboxItem) {
        return res.status(404).json({
          status: 'error',
          message: 'Inbox item not found',
        });
      }

      let result: any = {};

      switch (action) {
        case 'convert_to_task':
          if (!taskData) {
            return res.status(400).json({
              status: 'error',
              message: 'Task data is required for conversion',
            });
          }

          // Create task from inbox item
          const task = await prisma.task.create({
            data: {
              title: taskData.title,
              content: taskData.content || inboxItem.content,
              dueDate: taskData.dueDate ? new Date(taskData.dueDate) : undefined,
              effortEstimate: taskData.effortEstimate,
              projectId: taskData.projectId,
              tagId: taskData.tagId,
              userId: req.user.id,
            },
          });

          // Mark inbox item as processed
          await prisma.inboxItem.update({
            where: { id },
            data: {
              status: 'PROCESSED',
              processedAt: new Date(),
            },
          });

          result = { task };
          break;

        case 'delete':
          // Delete inbox item
          await prisma.inboxItem.delete({
            where: { id },
          });
          break;

        case 'defer':
          // Mark as deferred
          await prisma.inboxItem.update({
            where: { id },
            data: {
              status: 'DEFERRED',
              processedAt: new Date(),
            },
          });
          break;
      }

      res.json({
        status: 'success',
        message: `Inbox item ${action.replace('_', ' ')}d successfully`,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete an inbox item
   */
  static async deleteInboxItem(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const { id } = idParamSchema.parse(req.params);

      // Delete inbox item (only if it belongs to the user)
      const deletedItem = await prisma.inboxItem.deleteMany({
        where: {
          id,
          userId: req.user.id,
        },
      });

      if (deletedItem.count === 0) {
        return res.status(404).json({
          status: 'error',
          message: 'Inbox item not found',
        });
      }

      res.json({
        status: 'success',
        message: 'Inbox item deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }
}
